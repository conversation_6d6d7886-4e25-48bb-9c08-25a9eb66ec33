<?php

namespace App\Http\Livewire\Main\Auth\Social\Casdoor;

use App\Models\User;
use App\Utils\Uploader\ImageUploader;
use Livewire\Component;
use Lara<PERSON>\Socialite\Facades\Socialite;

class CallbackComponent extends Component
{
    /**
     * Mount component
     *
     * @return void
     */
    public function mount()
    {
        try {

            // Log configuration for debugging
            \Log::info('Casdoor OAuth Configuration Check', [
                'server_url' => config('services.casdoor.server_url', 'NOT SET'),
                'client_id' => config('services.casdoor.client_id', 'NOT SET'),
                'client_secret' => config('services.casdoor.client_secret') ? 'SET' : 'NOT SET',
                'application_name' => config('services.casdoor.application_name', 'NOT SET'),
                'redirect' => config('services.casdoor.redirect'),
                'config_exists' => 'YES'
            ]);

            // Get user from casdoor
            $user = Socialite::driver('casdoor')->stateless()->user();

            // Get user data
            $name     = $user->getName();
            $nickname = $user->getNickname();
            $email    = $user->getEmail();
            $avatar   = $user->getAvatar();

            // Log received user data for debugging
            \Log::info('Casdoor OAuth User Data Received', [
                'name' => $name,
                'nickname' => $nickname,
                'email' => $email,
                'avatar' => $avatar,
                'provider_id' => $user->getId(),
                'raw_user' => $user->getRaw()
            ]);

            // Check if email exists
            if (!$email) {

                // Error
                session()->flash('message', __('messages.t_we_could_not_get_ur_email_from_provider'));
                session()->flash('type', 'error');

                // Redirect to login
                return $this->redirect('/auth/login');

            }

            // Check if username exists
            $is_username_exists = User::where('username', $nickname)->exists();

            // Check if username exists
            if ($is_username_exists) {
                $username = $nickname . "_" . substr(md5(microtime()),rand(0,26),4);
            } else {
                $username = $nickname;
            }

            // Check if user has avatar
            if ($avatar) {
                
                // Save user avatar
                $avatar_id = ImageUploader::fromUrl($avatar, 'avatars');

            } else {
                $avatar_id = null;
            }

            // Now we can create this user
            $save = User::firstOrCreate(
                [
                    'email'         => $email,
                    'provider_id'   => $user->getId(),
                    'provider_name' => "casdoor"
                ],
                [
                    'uid'               => uid(),
                    'username'          => $username,
                    'email'             => $email,
                    'email_verified_at' => now(),
                    'password'          => null, // OAuth users don't need password
                    'account_type'      => 'buyer',
                    'avatar_id'         => $avatar_id,
                    'level_id'          => 1,
                    'provider_name'     => 'casdoor',
                    'provider_id'       => $user->getId(),
                    'fullname'          => $name ? $name : null,
                    'status'            => 'active',
                    'balance_net'       => '0',
                    'balance_withdrawn' => '0',
                    'balance_purchases' => '0',
                    'balance_pending'   => '0',
                    'balance_available' => '0'
                ]
            );

            // Log user creation result
            \Log::info('Casdoor OAuth User Creation Result', [
                'user_created' => $save->wasRecentlyCreated,
                'user_id' => $save->id,
                'user_email' => $save->email,
                'user_username' => $save->username,
                'user_status' => $save->status,
                'user_level_id' => $save->level_id
            ]);

            // Login this user
            auth()->login($save, true);

            // Log successful login for debugging
            \Log::info('Casdoor OAuth Success: User logged in', [
                'user_id' => $save->id,
                'email' => $save->email,
                'username' => $save->username,
                'provider_id' => $save->provider_id,
                'is_authenticated' => auth()->check()
            ]);

            // Redirect to home page
            return $this->redirect('/');

        } catch (\Throwable $th) {

            // Log the error for debugging
            \Log::error('Casdoor OAuth Error: ' . $th->getMessage(), [
                'file' => $th->getFile(),
                'line' => $th->getLine(),
                'trace' => $th->getTraceAsString()
            ]);

            // Error
            session()->flash('message', __('messages.t_toast_something_went_wrong') . ' Error: ' . $th->getMessage());
            session()->flash('type', 'error');

            // Redirect to login
            return $this->redirect('/auth/login');

        }
    }

    /**
     * Render component
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('livewire.main.auth.social.casdoor.callback');
    }
}
