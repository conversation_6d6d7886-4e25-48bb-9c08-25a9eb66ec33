<?php

namespace App\Http\Livewire\Main\Auth\Social\Casdoor;

use Livewire\Component;
use Laravel\Socialite\Facades\Socialite;
use Artesaos\SEOTools\Traits\SEOTools as SEOToolsTrait;

class RedirectComponent extends Component
{
    use SEOToolsTrait;
    /**
     * Init component
     *
     * @return void
     */
    public function mount()
    {
        // SEO
        $separator   = settings('general')->separator;
        $title       = __('messages.t_casdoor_login') . " $separator " . settings('general')->title;
        $description = settings('seo')->description;
        $ogimage     = src( settings('seo')->ogimage );

        $this->seo()->setTitle( $title );
        $this->seo()->setDescription( $description );
        $this->seo()->setCanonical( url()->current() );
        $this->seo()->opengraph()->setTitle( $title );
        $this->seo()->opengraph()->setDescription( $description );
        $this->seo()->opengraph()->setUrl( url()->current() );
        $this->seo()->opengraph()->setType('website');
        $this->seo()->opengraph()->addImage( $ogimage );
        $this->seo()->twitter()->setImage( $ogimage );
        $this->seo()->twitter()->setUrl( url()->current() );
        $this->seo()->twitter()->setSite( "@" . settings('seo')->twitter_username );
        $this->seo()->twitter()->addValue('card', 'summary_large_image');
        $this->seo()->metatags()->addMeta('fb:page_id', settings('seo')->facebook_page_id, 'property');
        $this->seo()->metatags()->addMeta('fb:app_id', settings('seo')->facebook_app_id, 'property');
        $this->seo()->metatags()->addMeta('robots', 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1', 'name');
        $this->seo()->jsonLd()->setTitle( $title );
        $this->seo()->jsonLd()->setDescription( $description );
        $this->seo()->jsonLd()->setUrl( url()->current() );
        $this->seo()->jsonLd()->setType('WebSite');

        // Get redirect url
        $url = Socialite::driver('casdoor')->stateless()->redirect()->getTargetUrl();

        // Redirect to login using Casdoor
        return redirect($url);
    }
}
