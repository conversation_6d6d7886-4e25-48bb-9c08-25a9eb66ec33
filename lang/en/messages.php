<?php

    return [
        "t_create_account"                                         => "Create account",
        "t_sign_in"                                                => "Sign in",
        "t_email_address"                                          => "E-mail address",
        "t_enter_email_address"                                    => "Enter e-mail address",
        "t_username"                                               => "Username",
        "t_enter_username"                                         => "Enter username",
        "t_password"                                               => "Password",
        "t_enter_password"                                         => "Enter password",
        "t_select_account_type"                                    => "Select account type",
        "t_iam_freelancer"                                         => "I am a freelancer",
        "t_iam_buyer"                                              => "I am a buyer",
        "t_signup"                                                 => "Sign up",
        "t_account_settings"                                       => "Account settings",
        "t_my_orders"                                              => "My orders",
        "t_messages"                                               => "Messages",
        "t_logout"                                                 => "Logout",
        "t_verification_center"                                    => "Verification center",
        "t_seller_dashboard"                                       => "Seller dashboard",
        "t_become_a_seller"                                        => "Become a seller",
        "t_login"                                                  => "Login",
        "t_choose_document_type"                                   => "Choose document type",
        "t_government_issued_id"                                   => "Government-issued ID",
        "t_driver_license"                                         => "Driver license",
        "t_passport"                                               => "Passport",
        "t_next_step"                                              => "Next step",
        "t_please_select_a_valid_document_type"                    => "Please select a valid document type",
        "t_upload_doc_front_side"                                  => "Upload document front side",
        "t_verification_allowed_mimes_size"                        => "JPG, JPEG and PNG (Max 5MB)",
        "t_upload_doc_back_side"                                   => "Upload document back side",
        "t_finish"                                                 => "Finish",
        "t_verification_selfie_page_msg"                           => "In order to continue your verification, we need to take a selfie ID verification. Please allow your camera to continue",
        "t_take_a_snapshot"                                        => "Take a snapshot",
        "t_verification_status"                                    => "Verification status",
        "t_account_verified"                                       => "Account verified",
        "t_verified_at"                                            => "Verified at",
        "t_verification_documents"                                 => "Verification documents",
        "t_back"                                                   => "Back",
        "t_unable_to_take_more_selfies"                            => "Unable to take more snapsots, please refresh this page",
        "t_verification_center_subtitle"                           => "Help us make our marketplace a safe place by verifying your account",
        "t_verification_files_received_success"                    => "We have successfully received your files and we will get back to you soon",
        "t_verification_pending"                                   => "Verification pending",
        "t_verification_declined"                                  => "Verification declined",
        "t_send_files_again"                                       => "Send files again",
        "t_declined_at"                                            => "Declined at",
        "t_verification_date"                                      => "Verification date",
        "t_selfie_photo"                                           => "Selfie photo",
        "t_download"                                               => "Download",
        "t_government_issued_id_frontside"                         => "Government-issued ID (front side)",
        "t_government_issued_id_backside"                          => "Government-issued ID (back side)",
        "t_driver_license_frontside"                               => "Driver's license (front side)",
        "t_driver_license_backside"                                => "Driver's license (back side)",
        "t_account_settings_subtitle"                              => "Update your profile settings",
        "t_update"                                                 => "Update",
        "t_avatar"                                                 => "Avatar",
        "t_change"                                                 => "Change",
        "t_remove"                                                 => "Remove",
        "t_fullname"                                               => "Fullname",
        "t_enter_fullname"                                         => "Enter fullname",
        "t_headline"                                               => "Headline",
        "t_enter_your_headline"                                    => "Enter your headline",
        "t_bio"                                                    => "Bio",
        "t_enter_your_bio"                                         => "Enter your bio",
        "t_enter_your_current_password"                            => "Enter your current password",
        "t_toast_form_validation_error"                            => "whoops there were some problems with your inputs",
        "t_toast_something_went_wrong"                             => "Oops! Something went wrong. Please try again",
        "t_ur_current_pass_does_not_match"                         => "Your current password does not match",
        "t_ur_account_settings_updated"                            => "Your account settings has been successfully updated",
        "t_ur_profile_pic_removed_success"                         => "Your profile picture has been successfully removed",
        "t_change_password"                                        => "Change password",
        "t_ur_account_password_updated"                            => "Your password has been successfully updated",
        "t_change_password_subtitle"                               => "Make a strong password to keep your account safe",
        "t_current_password"                                       => "Current password",
        "t_enter_current_password"                                 => "Enter current password",
        "t_new_password"                                           => "New password",
        "t_enter_new_password"                                     => "Enter new password",
        "t_password_confirmation"                                  => "Password confirmation",
        "t_enter_password_confirmation"                            => "Enter password confirmation",
        "t_create_new_service"                                     => "Create",
        "t_dashboard"                                              => "Dashboard",
        "t_users"                                                  => "Users",
        "t_browse_all"                                             => "Browse all",
        "t_create_new"                                             => "Create new",
        "t_categories"                                             => "Categories",
        "t_subcategories"                                          => "Subcategories",
        "t_admins"                                                 => "Admins",
        "t_gigs"                                                   => "Gigs",
        "t_create_category"                                        => "Create category",
        "t_create_category_subtitle"                               => "Inset your category data to create it",
        "t_category_name"                                          => "Category name",
        "t_enter_category_name"                                    => "Enter category name",
        "t_category_slug"                                          => "Category slug",
        "t_enter_category_slug"                                    => "Enter category slug",
        "t_description"                                            => "Description",
        "t_enter_description"                                      => "Enter description",
        "t_create"                                                 => "Create",
        "t_click_to_choose_a_file"                                 => "Click to choose a file",
        "t_category_icon"                                          => "Category icon",
        "t_category_image"                                         => "Category image",
        "t_show_cat_gigs_in_home_checkbox"                         => "Show services in this categories in home page",
        "t_category_visibility"                                    => "Category visibility",
        "t_toast_operation_success"                                => "The operation was successful",
        "t_category"                                               => "Category",
        "t_visibility"                                             => "Visibility",
        "t_visible"                                                => "Visible",
        "t_not_visible"                                            => "Not visible",
        "t_options"                                                => "Options",
        "t_edit"                                                   => "Edit",
        "t_delete"                                                 => "Delete",
        "t_page_previous"                                          => "Previous",
        "t_page_next"                                              => "Next",
        "t_no_data_to_show_now"                                    => "There are no data to show right now",
        "t_social_media_image"                                     => "Social media image",
        "t_edit_category"                                          => "Edit category",
        "t_edit_category_subtitle"                                 => "Update category data",
        "t_parent_category"                                        => "Parent category",
        "t_choose_parent_category"                                 => "Choose parent category",
        "t_edit_subcategory"                                       => "Edit subcategory",
        "t_create_subcategory"                                     => "Create subcategory",
        "t_edit_subcategory_subtitle"                              => "Make changes on this subcategory",
        "t_subcategory"                                            => "Subcategory",
        "t_create_subcategory_subtitle"                            => "Create a subcategory",
        "t_subcategory_name"                                       => "Subcategory name",
        "t_enter_subcategory_name"                                 => "Enter subcategory name",
        "t_subcategory_slug"                                       => "Subcategory slug",
        "t_enter_subcategory_slug"                                 => "Enter subcategory slug",
        "t_subcategory_icon"                                       => "Subcategory icon",
        "t_subcategory_image"                                      => "Subcategory image",
        "t_currency_settings"                                      => "Currency settings",
        "t_settings"                                               => "Settings",
        "t_currency_settings_subtitle"                             => "Update your default currency settings",
        "t_currency_name"                                          => "Currency name",
        "t_enter_currency_name"                                    => "Enter currency name",
        "t_exchange_rate"                                          => "Exchange rate",
        "t_enter_exchange_rate_to_usd"                             => "Enter exchange rate to us dollar",
        "t_currency_code"                                          => "Currency code",
        "t_choose_currency_code"                                   => "Choose currency code",
        "t_overview"                                               => "Overview",
        "t_create_gig_overview_subtitle"                           => "Describe your service",
        "t_choose_subcategory"                                     => "Choose subcategory",
        "t_pricing"                                                => "Pricing",
        "t_description_and_faq"                                    => "Description & FAQ",
        "t_requirements"                                           => "Requirements",
        "t_gallery"                                                => "Gallery",
        "t_back_to_homepage"                                       => "Back to homepage",
        "t_upgrade_seo"                                            => "Upgrade seo",
        "t_seo_title"                                              => "Seo title",
        "t_enter_seo_title"                                        => "Enter seo title",
        "t_seo_description"                                        => "Seo description",
        "t_enter_seo_description"                                  => "Enter seo description",
        "t_save_and_continue"                                      => "Save & continue",
        "t_faq"                                                    => "FAQ",
        "t_title"                                                  => "Title",
        "t_do_something_im_really_good_at"                         => "do something I'm really good at",
        "t_briefly_describe_ur_gig"                                => "Briefly describe your Gig",
        "t_add_service_upgrade"                                    => "Add service upgrade",
        "t_upgrade_title"                                          => "Upgrade title",
        "t_enter_upgrade_title"                                    => "Enter upgrade title",
        "t_upgrade_price"                                          => "Upgrade price",
        "t_enter_upgrade_price"                                    => "Enter upgrade price",
        "t_add"                                                    => "Add",
        "t_upgrade_needs_extra_days"                               => "This upgrade needs extra days for delivery",
        "t_i_will"                                                 => "I will",
        "t_describe_ur_offering"                                   => "describe your offering",
        "t_for_and_extra"                                          => "For and extra",
        "t_for_and_extra_price"                                    => "For and extra",
        "t_and_an_additional_days"                                 => "And an additional",
        "t_none"                                                   => "None",
        "t_1_day"                                                  => "1 day",
        "t_2_days"                                                 => "2 days",
        "t_3_days"                                                 => "3 days",
        "t_4_days"                                                 => "4 days",
        "t_5_days"                                                 => "5 days",
        "t_6_days"                                                 => "6 days",
        "t_1_week"                                                 => "1 week",
        "t_2_weeks"                                                => "2 weeks",
        "t_3_weeks"                                                => "3 weeks",
        "t_1_month"                                                => "1 month",
        "t_price_placeholder_0_00"                                 => "0.00",
        "t_price"                                                  => "Price",
        "t_delivery_time"                                          => "Delivery time",
        "t_choose_delivery_time"                                   => "Choose delivery time",
        "t_min_delivery_days"                                      => "Minimum delivery days",
        "t_max_delivery_days"                                      => "Maximum delivery days",
        "t_custom_delivery_range"                                  => "Delivery time range",
        "t_or_choose_quick_option"                                 => "Or choose a quick option",
        "t_days_placeholder"                                       => "Days",
        "t_create_gig_pricing_subtitle"                            => "Set price for your gig, add extra services",
        "t_upgrade_number"                                         => "Upgrade #:number",
        "t_remove_upgrade"                                         => "Remove upgrade",
        "t_faq_full"                                               => "Frequently Asked Questions",
        "t_create_gig_faq_subtitle"                                => "Add Questions & Answers for your buyers",
        "t_add_faq"                                                => "Add FAQ",
        "t_answer"                                                 => "Answer",
        "t_faq_answer_example"                                     => "i.e. Yes, I also translate from English to French.",
        "t_faq_question_example"                                   => "i.e. Do you translate to English as well?",
        "t_no_faq_yet"                                             => "No FAQ yet",
        "t_no_faq_yet_subtitle"                                    => "Here you can add answers to the most commonly asked questions. Your FAQs will be displayed in your Gig page",
        "t_create_gig_requirements_subtitle"                       => "Get all the information you need from buyers to get started",
        "t_add_requirement"                                        => "Add requirement",
        "t_request_necessary_details_such_dimensions"              => "Request necessary details such as dimensions, brand guidelines, and more",
        "t_add_question"                                           => "Add question",
        "t_get_it_from"                                            => "Get it from",
        "t_free_text"                                              => "Free text",
        "t_multiple_choice"                                        => "Multiple choice",
        "t_attachment"                                             => "Attachment",
        "t_multiple_choices"                                       => "Multiple choices",
        "t_add_option"                                             => "Add option",
        "t_add_new_option"                                         => "Add new option",
        "t_required"                                               => "Required",
        "t_add_a_video"                                            => "Add a video",
        "t_get_noticed_by_right_buyers_images"                     => "Get noticed by the right buyers with visual examples of your services",
        "t_images"                                                 => "Images",
        "t_documents"                                              => "Documents",
        "t_show_some_of_best_work_doc_pdfs_only"                   => "Show some of the best work you created in a document (PDFs only)",
        "t_add_youtube_video"                                      => "Add a Youtube video",
        "t_youtube_placeholder"                                    => "https://www.youtube.com/watch?v=xxxxxxxxxxx",
        "t_youtube_video_not_exists"                               => "This Youtube video does not exist",
        "t_youtube_video_invalid"                                  => "Invalid Youtube link, Please try again",
        "t_youtube_video_has_been_successfully_added"              => "Youtube video has been successfully added",
        "t_capture_buyer_attention_with_video"                     => "Capture buyers' attention with a video that showcases your service",
        "t_video"                                                  => "Video",
        "t_remove_video"                                           => "Remove video",
        "t_step_number_1"                                          => "Step 1",
        "t_step_number_2"                                          => "Step 2",
        "t_step_number_3"                                          => "Step 3",
        "t_step_number_4"                                          => "Step 4",
        "t_seo"                                                    => "SEO",
        "t_search_engine_gig_preview"                              => "Search engine Gig preview",
        "t_reviews"                                                => "Reviews",
        "t_get_started_by_create_new_faq"                          => "Get started by creating a new frequently asked question",
        "t_search_tags"                                            => "Search tags",
        "t_press_enter_to_add_tags"                                => "Press ENTER to add tags",
        "t_max_tags_letters_numbers_only"                          => ":max tags maximum. Use letters and numbers only",
        "t_faq_added_successfully"                                 => "Your question and answer has been successfully added",
        "t_data_has_been_saved"                                    => "Data has been successfully saved",
        "t_gig_created_successfully"                               => "Gig has been successfully created",
        "t_gig_created"                                            => "Gig created",
        "t_gig_created_subtitle"                                   => "You gig has been successfully posted, you can now share it and start receiving orders",
        "t_view_gig"                                               => "View gig",
        "t_home"                                                   => "Home",
        "t_gig_posted_on_date"                                     => "Posted on :date",
        "t_number_orders_in_queue"                                 => ":number orders in queue",
        "t_number_reviews"                                         => ":number reviews",
        "t_report_this_gig"                                        => "Report this gig",
        "t_reason"                                                 => "Reason",
        "t_let_us_know_why_u_report_this_gig"                      => "Let us know why you would like to report this Gig",
        "t_report"                                                 => "Report",
        "t_pls_login_or_register_to_report_this_gig"               => "Please login or sign up to report this gig",
        "t_gig_owner_cant_report_his_gig"                          => "You cannot report your own gigs",
        "t_looks_like_alrdy_reported_this_gig"                     => "It looks like you already reported this gig",
        "t_gig_reported_successfully"                              => "Thank you! your request has been successfully sent",
        "t_share_this_gig"                                         => "Share this gig",
        "t_facebook"                                               => "Facebook",
        "t_twitter"                                                => "Twitter",
        "t_linkedin"                                               => "Linkedin",
        "t_whatsapp"                                               => "WhatsApp",
        "t_copy_link"                                              => "Copy link",
        "t_copied"                                                 => "Copied",
        "t_view_profile"                                           => "View profile",
        "t_contact_me"                                             => "Contact me",
        "t_add_to_cart"                                            => "Add to cart",
        "t_available_upgrades_for_this_service"                    => "Available upgrades for this service",
        "t_delivery_time_will_be_increased_by_extra"               => "Delivery time will be increased by an extra :time",
        "t_no_changes_delivery_time"                               => "Delivery time won't change",
        "t_service_attached_docs_subtitle"                         => "See attached documents below",
        "t_about_this_gig"                                         => "About this gig",
        "t_read_gig_desc_carfully_subtitle"                        => "Read carefully description below to understand what you will get",
        "t_gig_reviews_section_subtitle"                           => "Read customer reviews below",
        "t_selected_quantity_is_not_correct"                       => "The selected quantity must be between 1 and 10",
        "t_view_cart"                                              => "View cart",
        "t_product_added_to_cart"                                  => "Gig added to cart",
        "t_continue_shopping"                                      => "Continue shopping",
        "t_shopping_cart"                                          => "Shopping cart",
        "t_review_your_cart_before_proceed_to_checkout"            => "Review your shopping cart before proceed to checkout",
        "t_extra_days_delivery_time_short"                         => "+ :time on delivery time",
        "t_item_removed_from_cart_success"                         => "Item has been successfully removed from your cart",
        "t_ur_cart_is_empty"                                       => "Your shopping cart is empty",
        "t_ur_cart_is_empty_subtitle"                              => "Looks like you haven't added anything to your cart yet",
        "t_subtotal"                                               => "Subtotal",
        "t_tax_estimate"                                           => "Tax estimate",
        "t_total"                                                  => "Total",
        "t_checkout"                                               => "Checkout",
        "t_cart_summary"                                           => "Cart summary",
        "t_these_fees_cover_costs_tooltip"                         => "These fees cover the costs of payment gateways and help us operate the site and support you",
        "t_an_item_in_your_cart_doesnot_exist"                     => "Looks like an item in your cart no longer exists",
        "t_an_upgrade_in_item_in_cart_not_found"                   => "An upgrade in your cart is no longer exists",
        "t_order_summary"                                          => "Order summary",
        "t_order_summary_subtitle"                                 => "Your order invoice details",
        "t_enter_ur_billing_infor_below"                           => "Enter your billing information below",
        "t_invoice"                                                => "Invoice",
        "t_billing_information"                                    => "Billing information",
        "t_firstname"                                              => "Firstname",
        "t_enter_firstname"                                        => "Enter firstname",
        "t_lastname"                                               => "Lastname",
        "t_enter_lastname"                                         => "Enter lastname",
        "t_company"                                                => "Company",
        "t_enter_company_optional"                                 => "Enter company (optional)",
        "t_address"                                                => "Address",
        "t_enter_address"                                          => "Enter address",
        "t_payment"                                                => "Payment",
        "t_choose_ur_prefered_payment_method"                      => "Choose your preferred payment method",
        "t_credit_card"                                            => "Credit card",
        "t_paypal"                                                 => "Paypal",
        "t_name_on_card"                                           => "Name on card",
        "t_enter_name_credit_card"                                 => "Enter name on credit card",
        "t_card_number"                                            => "Card number",
        "t_card_number_placeholder"                                => "xxxx xxxx xxxx xxxx",
        "t_expiration_date"                                        => "Expiration date",
        "t_mm_yyyy"                                                => "MM / YYYY",
        "t_cc_cvc"                                                 => "CVC",
        "t_cc_cvc_placeholder"                                     => "123",
        "t_place_order"                                            => "Place order",
        "t_ur_transaction_is_secure"                               => "Your transaction is secure",
        "t_back_to_shopping_cart"                                  => "Back to shopping cart",
        "t_paypal_payment_success_click_place"                     => "Your payment has succeeded, you can now place your order",
        "t_sorry_we_couldnt_handle_ur_paypal_payment"              => "Sorry we couldn't handle your PayPal payment. Please contact us",
        "t_amount_from_paypal_not_correct_as_cart"                 => "The total amount from your payment is not as the total from your cart, Please contact us",
        "t_pls_enter_valid_cc_data"                                => "Please enter a valid credit card data",
        "t_payment_cc_failed_try_again"                            => "Payment failed, please try again",
        "t_please_choose_a_payment_method"                         => "Please choose a payment method first",
        "t_order_number"                                           => "Order number",
        "t_date_placed"                                            => "Date placed",
        "t_total_amount"                                           => "Total amount",
        "t_quantity"                                               => "Quantity",
        "t_expected_delivery_date"                                 => "Expected delivery date",
        "t_status"                                                 => "Status",
        "t_pending"                                                => "Pending",
        "t_in_the_process"                                         => "In the process",
        "t_delivered"                                              => "Delivered",
        "t_canceled"                                               => "Canceled",
        "t_refunded"                                               => "Refunded",
        "t_actions"                                                => "Actions",
        "t_contact_seller"                                         => "Contact seller",
        "t_request_refund"                                         => "Request refund",
        "t_delivered_work"                                         => "Delivered work",
        "t_cancel_service"                                         => "Cancel service",
        "t_send_requirements"                                      => "Submit requirements",
        "t_u_have_send_reqs_asap_to_seller"                        => "You have to send the required information to seller to start processing your order as soon as possible",
        "t_buy_again"                                              => "Buy again",
        "t_view_invoice"                                           => "View invoice",
        "t_orders_history"                                         => "Orders history",
        "t_orders_history_subtitle"                                => "Check the status of recent orders, manage their items",
        "t_attention_needed"                                       => "Attention needed",
        "t_submit_ur_info_now_seller_start_order"                  => "Submit your info now so that the seller may begin the order",
        "t_hey_before_i_start_working_on_order_msg"                => "Hey, before I start working on this order I need to receive the following details",
        "t_type_ur_message_here"                                   => "Type your message here",
        "t_back_to_orders"                                         => "Back to orders",
        "t_gig_id"                                                 => "Gig id",
        "t_submitted_files_preview"                                => "Submitted information preview",
        "t_submitted_files_preview_subtitle"                       => "Information you will submit to seller will be visible below, you can change them later",
        "t_submit"                                                 => "Submit",
        "t_uploading"                                              => "Uploading",
        "t_pls_wait_until_uploading_finish"                        => "Please wait until uploading finish",
        "t_error_while_uploading_your_file"                        => "Error while uploading your file",
        "t_exception_REQUIRED_INPUT_NOT_EXISTS_IN_REQUEST"         => "You need to fill all required inputs first",
        "t_exception_REQUIRED_INPUT_TEXT_LENGTH_VALIDATION_FAILED" => "A text field must not be more than 500 characters",
        "t_exception_REQUIRED_CHOICE_MULTIPLE_VALUES_NOT_EXISTS"   => "A selected option does not exists in our records",
        "t_exception_REQUIRED_CHOICE_VALUE_NOT_EXISTS"             => "A selected option does not exists in our records",
        "t_exception_REQUIRED_FILE_VALIDATION_FAILED"              => "Validation failed on selected files, please try again",
        "t_remember_resubmit_files_reset_expected_delivery_date"   => "Remember that re-submitting the required information again will reset the expected delivery date again and will delete old records and files",
        "t_resubmit"                                               => "Re-submit",
        "t_user_already_submitted_requirements"                    => "It looks like your already submitted the required information to seller. Please try to re-submit them again",
        "t_required_info_submitted_success"                        => "The required information have been sent to the seller to start processing your order",
        "t_u_didnt_submit_any_info_yet_requirements"               => "It looks like you did not send any required information to re-submit them",
        "t_resubmit_requirements_success_msg"                      => "You can now send new required sinformation to seller",
        "t_selected_file_extension_is_not_allowed"                 => "Selected file extension is not allowed",
        "t_selected_file_size_big"                                 => "The selected file size is too large",
        "t_u_cant_submit_requirements_for_item"                    => "You cannot submit required information for this item",
        "t_ur_orders_history_is_empty_right_now"                   => "Your orders history is empty right now",
        "t_go_shopping"                                            => "Go shopping",
        "t_get_started_now"                                        => "Get started now",
        "t_become_a_seller_subtitle"                               => "Become a top seller on our platform and start making profits online",
        "t_seller_feature_title_1"                                 => "Feature 1",
        "t_seller_feature_title_2"                                 => "Feature 2",
        "t_seller_feature_title_3"                                 => "Feature 3",
        "t_seller_feature_title_4"                                 => "Feature 4",
        "t_seller_feature_title_5"                                 => "Feature 5",
        "t_seller_feature_title_6"                                 => "Feature 6",
        "t_seller_feature_title_7"                                 => "Feature 7",
        "t_seller_feature_title_8"                                 => "Feature 8",
        "t_seller_feature_title_9"                                 => "Feature 9",
        "t_seller_feature_title_10"                                => "Feature 10",
        "t_seller_feature_title_11"                                => "Feature 11",
        "t_seller_feature_title_12"                                => "Feature 12",
        "t_seller_feature_desc_1"                                  => "Feature description 1",
        "t_seller_feature_desc_2"                                  => "Feature description 2",
        "t_seller_feature_desc_3"                                  => "Feature description 3",
        "t_seller_feature_desc_4"                                  => "Feature description 4",
        "t_seller_feature_desc_5"                                  => "Feature description 5",
        "t_seller_feature_desc_6"                                  => "Feature description 6",
        "t_seller_feature_desc_7"                                  => "Feature description 7",
        "t_seller_feature_desc_8"                                  => "Feature description 8",
        "t_seller_feature_desc_9"                                  => "Feature description 9",
        "t_seller_feature_desc_10"                                 => "Feature description 10",
        "t_seller_feature_desc_11"                                 => "Feature description 11",
        "t_seller_feature_desc_12"                                 => "Feature description 12",
        "t_my_gigs"                                                => "My gigs",
        "t_earnings"                                               => "Earnings",
        "t_withdrawals"                                            => "Withdrawals",
        "t_refunds"                                                => "Refunds",
        "t_number_pending_orders"                                  => ":number pending order(s)",
        "t_signed_up_on_date"                                      => "Signed up on :date",
        "t_total_sales_number"                                     => ":number sale(s)",
        "t_publish_new_gig"                                        => "Publish new gig",
        "t_withdrawal_earnings"                                    => "Withdrawal earnings",
        "t_boost_ur_productivity"                                  => "Boost your productivity",
        "t_boost_ur_productivity_subtitle"                         => "The key to your success on our platform is the brand you build for yourself through your reputation with us. We gathered some tips and resources to help you become a leading seller on our platform",
        "t_share_ur_gigs"                                          => "Share your gigs",
        "t_tap_into_the_power_of_social_media_text"                => "Tap into the power of social media by sharing your Gig, and grow your impact",
        "t_get_more_skills"                                        => "Get more skills",
        "t_hone_ur_skills_and_expand_knowledge"                    => "Hone your skills and expand your knowledge with online courses. You'll be able to offer more services and gain more exposure with every course completed",
        "t_contact_us"                                             => "Contact us",
        "t_become_a_successful_seller"                             => "Become a successful seller",
        "t_learn_how_to_create_an_outstanding_service_experience"  => "Learn how to create an outstanding service experience for your buyers and grow your career as an online freelancer in our platform",
        "t_gig"                                                    => "Gig",
        "t_number_days_for_delivery"                               => ":number day(s) for delivery",
        "t_sales"                                                  => "Sales",
        "t_active"                                                 => "Active",
        "t_featured"                                               => "Featured",
        "t_deleted"                                                => "Deleted",
        "t_trending"                                               => "Trending",
        "t_boosted"                                                => "Boosted",
        "t_rating"                                                 => "Rating",
        "t_clicks"                                                 => "Clicks",
        "t_share"                                                  => "Share",
        "t_analytics"                                              => "Analytics",
        "t_profit"                                                 => "Profit",
        "t_waiting_for_requirements"                               => "Waiting for requirements",
        "t_order_details"                                          => "Order details",
        "t_contact_buyer"                                          => "Contact buyer",
        "t_deliver_work"                                           => "Deliver work",
        "t_view_requirements"                                      => "View requirements",
        "t_cancel_order"                                           => "Cancel order",
        "t_are_u_sure_u_want_to_cancel_order"                      => "Are you sure you want to cancel this order?",
        "t_upgrades"                                               => "Upgrades",
        "t_edit_gig"                                               => "Edit gig",
        "t_start_the_order"                                        => "Start order",
        "t_buyer_didnt_send_requirements_yet_continue"             => "We are waiting for the buyer to submit the requirements, until then the countdown for this order will not start",
        "t_cancel"                                                 => "Cancel",
        "t_i_have_all_info_needed"                                 => "I have all the information needed",
        "t_this_order_already_finished_cant_deliver"               => "This order already finished and you can't deliver work anymore",
        "t_u_cant_send_delivered_work_anymore_status_wrong"        => "You can't submit any files for this order because it not active",
        "t_deliver_completed_work"                                 => "Deliver completed work",
        "t_deliver_completed_work_subtitle"                        => "Submit the completed work to the buyer",
        "t_upload_work"                                            => "Upload work",
        "t_only_zip_allowed_max_size"                              => "Only .zip files allowed and maximum size of :size MB",
        "t_quick_response"                                         => "Quick response",
        "t_describe_ur_delivery_in_detail"                         => "Describe your delivery in detail",
        "t_u_have_to_select_deliverd_work_or_quick_response"       => "You must either upload a completed work or enter a quick response for the buyer",
        "t_looks_like_u_already_uploaded_completed_work"           => "It looks like you already uploaded a completed work",
        "t_resubmit_work_again"                                    => "Re-submit work again",
        "t_are_u_sure_u_want_to_resubmit_work_again"               => "Are you sure you want to re-submit the work again? the existing records will permanently deleted",
        "t_message"                                                => "Message",
        "t_send"                                                   => "Send",
        "t_gig_analytics"                                          => "Gig analytics",
        "t_back_to_gigs"                                           => "Back to gigs",
        "t_share_gig"                                              => "Share gig",
        "t_total_sales"                                            => "Total sales",
        "t_total_clicks"                                           => "Total clicks",
        "t_total_impressions"                                      => "Total impressions",
        "t_total_reviews"                                          => "Total reviews",
        "t_browsers"                                               => "Browsers",
        "t_browsers_chart_subtitle"                                => "Visits from most browsers",
        "t_os"                                                     => "Operating systems",
        "t_os_chart_subtitle"                                      => "Visits from most operating systems",
        "t_visits"                                                 => "Visits",
        "t_devices"                                                => "Devices",
        "t_devices_chart_subtitle"                                 => "Most visits by device type",
        "t_recent_orders"                                          => "Recent orders",
        "t_recent_orders_subtitle"                                 => "Latest 10 orders for this gig",
        "t_referrers"                                              => "Referrers",
        "t_referrers_subtitle"                                     => "Top referrers to your gig",
        "t_redirecting_dots"                                       => "Redirecting...",
        "t_redirecting_notification_alert"                         => "You are now leaving our website to another website which is out of our control. We are not responsible for any external Web sites",
        "t_proceed_with_caution"                                   => "Proceed with caution",
        "t_redirect_alert_msg_1"                                   => "Do not deal with any user outside the site or transfer amounts to him",
        "t_redirect_alert_msg_2"                                   => "Do not give your credentials, personal or financial information",
        "t_redirect_alert_msg_3"                                   => "Untrusted sites may contain malware or may imitate the design of other sites",
        "t_proceed"                                                => "Proceed",
        "t_most_visits_by_cities"                                  => "Most visits by cities",
        "t_cities"                                                 => "Cities",
        "t_visitors_map"                                           => "Visitors map",
        "t_visitors_map_subtitle"                                  => "Map of visitors to your gig",
        "t_published"                                              => "Published",
        "t_net_income"                                             => "Net income",
        "t_withdrawn"                                              => "Withdrawn",
        "t_used_for_purchases"                                     => "Used for Purchases",
        "t_pending_clearance"                                      => "Pending clearance",
        "t_available_for_withdrawal"                               => "Available for withdrawal",
        "t_date"                                                   => "Date",
        "t_for"                                                    => "For",
        "t_amount"                                                 => "Amount",
        "t_rejected"                                               => "Rejected",
        "t_paid"                                                   => "Paid",
        "t_make_withdrawal"                                        => "Make withdrawal",
        "t_paypal_email"                                           => "PayPal e-mail address",
        "t_enter_paypal_email"                                     => "Enter your PayPal e-mail address",
        "t_withdrawals_history"                                    => "Withdrawals history",
        "t_cant_withdraw_reason_pending_request"                   => "You cannot withdrawal right now because you have a pending withdrawal request",
        "t_cant_withdraw_reason_min_amount"                        => "The minimum amount you can withdraw from your account is :amount",
        "t_cant_withdraw_reason_period_24_hours"                   => "You can make only one withdrawal per day. Please try again later",
        "t_cant_withdraw_reason_period_7_days"                     => "You can make only one withdrawal per week. Please try again later",
        "t_cant_withdraw_reason_period_monthly"                    => "You can make only one withdrawal per month. Please try again later",
        "t_amount_to_withdraw"                                     => "Amount to withdraw",
        "t_available_balance_amount"                               => "Available for withdraw :amount",
        "t_ur_withdrawal_request_under_review"                     => "Your withdrawal request is currently under review",
        "t_congratulations"                                        => "Congratulations",
        "t_sigin_with"                                             => "Sign in with",
        "t_or"                                                     => "Or",
        "t_by_register_agree_terms_privacy"                        => "By signing up, you agree to our <a href=':terms_link' class='font-medium text-gray-900 hover:underline'>:terms_text</a> and <a href=':privacy_link' class='font-medium text-gray-900 hover:underline'>:privacy_text</a>",
        "t_send_message_agree_terms"                               => "By sending your message, you agree to our <a href=':terms_link' class='font-medium text-gray-900 hover:underline'>:terms_text</a> and <a href=':privacy_link' class='font-medium text-gray-900 hover:underline'>:privacy_text</a>",
        "t_by_continue_agree_terms_privacy"                        => "By continue, you agree to our <a href=':terms_link' class='font-medium text-gray-900 hover:underline'>:terms_text</a> and <a href=':privacy_link' class='font-medium text-gray-900 hover:underline'>:privacy_text</a>",
        "t_privacy_policy"                                         => "Privacy policy",
        "t_terms_of_service"                                       => "Terms of service",
        "t_register_verification_email_sent"                       => "We've sent and email to :email to verify your email address and activate your account. The link in the email will expire in :minutes minutes",
        "t_register_verification_admin_pending"                    => "Thank you for sign up, your account is under review now and we will notify you when it is activated",
        "t_verification_email_not_exists"                          => "This verification link is invalid. Please request new one",
        "t_verification_email_link_expired"                        => "This verification link has expired. Please request new one",
        "t_resend_verification_email"                              => "Resend verification email",
        "t_resend_verification_email_subtitle"                     => "To resend the verification email, please enter your email address below and click submit",
        "t_back_to_sign_in"                                        => "Back to sign in",
        "t_a_new_verification_link_has_been_sent_to_ur_email"      => "A new verification link has been sent to your email address",
        "t_ur_account_has_been_successfully_verified_email"        => "Your account has been successfully activated",
        "t_already_have_account"                                   => "Already have an account?",
        "t_socialite_error_email_exists"                           => "Oops! The email address you are trying to login with, already exists in our records",
        "t_register_slide_text_1"                                  => "Boost your <span class='text-primary-600'>earnings</span> by driving traffic to :app_name",
        "t_register_slide_text_2"                                  => "Find the perfect <span class='text-primary-600'>freelance</span> services for your business",
        "t_register_subtitle"                                      => "What do you need done? Find it on :app_name . Hire the best freelancers for any job, online, Millions of people use :app_name to turn their ideas into reality. Find professionals you can trust by browsing their samples of previous work and reading their profile reviews.",
        "t_remember_me"                                            => "Remember me",
        "t_forgot_password"                                        => "Forgot password?",
        "t_didnt_receive_confirm_email_resend_it"                  => "Didn't receive confirmation email? <a href=':url' class='font-medium text-gray-900 hover:underline'>Resend</a>",
        "t_invalid_login_credentials_pls_try_again"                => "Invalid login credentials. Please try again",
        "t_reset_ur_password"                                      => "Reset your password",
        "t_reset_ur_password_subtitle"                             => "Enter your email address and we will send you instructions on how to reset your password",
        "t_reset_password"                                         => "Reset password",
        "t_password_reset_link_sent_success"                       => "A password reset link has been sent to you via email",
        "t_password_reset_link_expired"                            => "This password reset link has expired. Please try again",
        "t_password_has_been_updated"                              => "Password has been successfully updated",
        "t_update_password"                                        => "Update password",
        "t_update_password_subtitle"                               => "Make sure to remember your password to login",
        "t_join_us"                                                => "Join us",
        "t_search"                                                 => "Search",
        "t_my_cart"                                                => "My cart",
        "t_support"                                                => "Support",
        "t_logged_in_as_username"                                  => "Logged in as :username!",
        "t_close"                                                  => "Close",
        "t_selected_lang_does_not_found"                           => "The selected language does not exists",
        "t_my_shopping_cart"                                       => "My shopping cart",
        "t_tax_fees_calculated_at_checkout"                        => "Tax fees calculated at checkout",
        "t_add_to_favorite"                                        => "Add to favorite",
        "t_pls_login_or_register_to_add_to_favovorite"             => "Please sign in or create an account to add this Gig to your favorite list",
        "t_this_gig_already_in_favorite_list"                      => "This Gig already in your favorite list",
        "t_gig_has_been_added_to_favorite_list"                    => "Gig has been successfully added to your favorite list",
        "t_remove_from_favorite"                                   => "Remove from favorite",
        "t_read_all_reviews"                                       => "Read all reviews",
        "t_gig_removed_from_ur_favorite_list"                      => "Gig has been removed from your favorite list",
        "t_footer_column_1"                                        => "Company",
        "t_footer_column_2"                                        => "Legal",
        "t_footer_column_3"                                        => "Links",
        "t_footer_column_4"                                        => "Support",
        "t_follow_us"                                              => "Follow us",
        "t_follow_us_on_facebook"                                  => "Follow us on Facebook",
        "t_follow_us_on_twitter"                                   => "Follow us on Twitter",
        "t_follow_us_on_instagram"                                 => "Follow us on Instagram",
        "t_follow_us_on_linkedin"                                  => "Follow us on Linkedin",
        "t_follow_us_on_pinterest"                                 => "Follow us on Pinterest",
        "t_follow_us_on_github"                                    => "Follow us on Github",
        "t_follow_us_on_youtube"                                   => "Follow us on Youtube",
        "t_change_language"                                        => "Change language",
        "t_contact_us_subtitle"                                    => "Need to get in touch with us? Fill the form below with your inquiry and our team will contact you soon",
        "t_name"                                                   => "Name",
        "t_enter_your_fullname"                                    => "Enter your fullname",
        "t_subject"                                                => "Subject",
        "t_enter_message_subject"                                  => "Enter message subject",
        "t_descibe_ur_message_in_details"                          => "Describe your message in details",
        "t_recaptcha_error_message"                                => "You must pass the reCaptcha test",
        "t_lets_talk"                                              => "Let's talk",
        "t_your_message_support_received_success"                  => "Thank you! We have received your message and we will get back to you soon",
        "t_what_service_are_u_looking_for_today"                   => "What service are you looking for today?",
        "t_sellers"                                                => "Sellers",
        "t_tags"                                                   => "Tags",
        "no_results_found"                                         => "No results found",
        "t_we_couldnt_find_anthing_search_term"                    => "We couldn't find anything with that term. Please try again",
        "t_press_enter_to_search_deeply"                           => "Press <kbd class='mx-2 flex h-5 px-1 items-center justify-center rounded border border-gray-400 bg-white font-semibold text-gray-900 sm:mx-2'>ENTER</kbd> <span class='hidden sm:inline'>for advanced search</span>",
        "t_pls_type_a_search_term_first"                           => "Please type a search term first",
        "t_become_a_seller_subtitle_card"                          => "Start your online business with us today as a professional seller and earn money.",
        "t_sort_by"                                                => "Sort by",
        "t_most_popular"                                           => "Most popular",
        "t_best_rating"                                            => "Best rating",
        "t_most_selling"                                           => "Most selling",
        "t_newest_first"                                           => "Newest first",
        "t_price_low_to_high"                                      => "Price: Low to High",
        "t_price_high_to_low"                                      => "Price: High to Low",
        "t_min_price"                                              => "Min price",
        "t_max_price"                                              => "Max price",
        "t_filter"                                                 => "Filter",
        "t_reset_filter"                                           => "Reset filter",
        "t_edit_profile"                                           => "Edit profile",
        "t_avatar_updated_successfully"                            => "Your profile avatar has been successfully updated",
        "t_approve"                                                => "Approve",
        "t_headline_updated_successfully"                          => "Your profile headline has been successfully updated",
        "t_online"                                                 => "Online",
        "t_linked_accounts"                                        => "Linked accounts",
        "t_connect_ur_social_media_accounts"                       => "Connect your social media accounts",
        "t_enter_facebook_profile"                                 => "Enter your Facebook profile",
        "t_enter_twitter_profile"                                  => "Enter your Twitter profile",
        "t_enter_dribbble_profile"                                 => "Enter your Dribbble profile",
        "t_enter_stackoverflow_profile"                            => "Enter your Stack Overflow profile",
        "t_enter_github_profile"                                   => "Enter your Github profile",
        "t_enter_youtube_profile"                                  => "Enter your Youtube profile",
        "t_enter_vimeo_profile"                                    => "Enter your Facebook profile",
        "t_dribbble"                                               => "Dribbble",
        "t_stackoverflow"                                          => "Stack Overflow",
        "t_github"                                                 => "Github",
        "t_youtube"                                                => "Youtube",
        "t_vimeo"                                                  => "Vimeo",
        "t_linked_accounts_has_been_updated"                       => "Linked accounts has been successfully updated",
        "t_skills"                                                 => "Skills",
        "t_let_ur_buyers_know_ur_skills"                           => "Let your people know your skills",
        "t_add_skill"                                              => "Add skill",
        "t_eg_voice_talent"                                        => "e.g Voice talent",
        "t_experience"                                             => "Experience",
        "t_beginner"                                               => "Beginner",
        "t_intermediate"                                           => "Intermediate",
        "t_expert"                                                 => "Expert",
        "t_add_skill_already_exists"                               => "This skill already exists in your profile",
        "t_skill_added_to_ur_profile"                              => "Skill has been successfully added to your profile",
        "t_delete_skill"                                           => "Delete skill",
        "t_edit_skill"                                             => "Edit skill",
        "t_skill_has_been_deleted_from_profile"                    => "Skill has been successfully deleted from your profile",
        "t_update_skill"                                           => "Update skill",
        "t_skill_updated"                                          => "Your skill has been successfully updated",
        "t_add_languages_u_speak"                                  => "Add languages you speak",
        "t_languages"                                              => "Languages",
        "t_u_dont_have_any_skills"                                 => "You don't have any skills yet",
        "t_choose_language"                                        => "Choose language",
        "t_language"                                               => "Language",
        "t_update_language"                                        => "Update language",
        "t_add_language"                                           => "Add language",
        "t_u_dont_have_any_languages"                              => "You don't have any languages yet",
        "t_basic"                                                  => "Basic",
        "t_conversational"                                         => "Conversational",
        "t_fluent"                                                 => "Fluent",
        "t_native"                                                 => "Native",
        "t_language_added_to_ur_profile"                           => "Language has been successfully added to your profile",
        "t_language_has_been_deleted_from_profile"                 => "Language has been successfully deleted from your profile",
        "t_add_language_already_exists"                            => "Language already exists in your profile",
        "t_language_updated"                                       => "Language has been successfully updated",
        "t_delete_language"                                        => "Delete language",
        "t_edit_language"                                          => "Edit language",
        "t_tell_us_more_about_ur_self"                             => "Tell us more about yourself",
        "t_pls_tell_us_about_ur_hobbies_etc"                       => "Please tell us about any hobbies, additional expertise, or anything else you'd like to add",
        "t_profile_description_updated"                            => "Your profile description has been successfully updated",
        "t_iam_currently"                                          => "I am currently",
        "t_available"                                              => "Available",
        "t_not_available"                                          => "Not available",
        "t_when_do_u_expect_tobe_ready_for_new_work"               => "When do you expect to be ready for new work?",
        "t_select_a_date"                                          => "Select a date",
        "t_add_a_message"                                          => "Add a message",
        "t_buyers_will_see_ur_message_when_visiting_ur_gigs"       => "Buyers will see your message when visiting your gig on profile page",
        "t_change_availability"                                    => "Change availability",
        "t_set_availability"                                       => "Set availability",
        "t_pls_select_availability_date_in_future"                 => "Please select a date in future",
        "t_ur_availability_settings_updated"                       => "Your availability settings has been successfully updated",
        "t_mm_dd_yyyy_example"                                     => "MM/DD/YYYY e.g. :example",
        "t_invalid_carbon_date_format"                             => "Invalid date format. Please try again",
        "t_member_since"                                           => "Member since",
        "t_current_level"                                          => "Current level",
        "t_n_a"                                                    => "N/A",
        "t_country"                                                => "Country",
        "t_availability"                                           => "Availability",
        "t_when_unavailable_u_wont_receive_orders"                 => "When unvailable, you won't be able to receive new orders",
        "t_unavailable"                                            => "Unavailable",
        "t_u_wont_be_able_to_receive_orders_until_date"            => "You won't be able to receive new orders or messages until <span class='font-semibold text-gray-900 dark:text-gray-200'>:date</span>",
        "t_update_profile"                                         => "Update profile",
        "t_these_info_will_appear_on_ur_public_profile"            => "These information will appear on your public profile. Use the links below to edit other settings",
        "t_get_verified"                                           => "Get verified",
        "t_report_user"                                            => "Report user",
        "t_report_user_reason_placeholder"                         => "Why would like to report this user?",
        "t_u_must_login_to_report_this_profile"                    => "You must be logged in to report this profile",
        "t_profile_has_been_successfully_reported"                 => "Profile has been successfully reported",
        "t_this_user_is_not_available_right_now_msg"               => "This user is not available right now, and he will be back on :date",
        "t_details"                                                => "Details",
        "t_more_details_about_this_user"                           => "More detailed information about this user",
        "t_level"                                                  => "Level",
        "t_last_delivery"                                          => "Last delivery",
        "t_pro"                                                    => "Expert",
        "t_user_skills_and_hobbies"                                => "User list of skills and hobbies",
        "t_follow_me_on_other_social_networks"                     => "Follow me on other socila networks",
        "t_list_of_languages_i_speak"                              => "List of languages I speak",
        "t_see_my_latest_works"                                    => "See my latest works",
        "t_portfolio"                                              => "Portfolio",
        "t_view_my_porfolio"                                       => "View my portfolio",
        "t_my_projects"                                            => "My projects",
        "t_create_project"                                         => "Create project",
        "t_project_id"                                             => "Project ID",
        "t_project"                                                => "Project",
        "t_back_to_projects"                                       => "Back to projects",
        "t_project_title"                                          => "Project title",
        "t_enter_project_title"                                    => "Enter project title",
        "t_project_description"                                    => "Project description",
        "t_enter_project_description"                              => "Enter project description",
        "t_project_thumbnail"                                      => "Project thumbnail",
        "t_project_link_optional"                                  => "Project link (optional)",
        "t_https_example_com"                                      => "https://www.example.com",
        "t_project_video_optional"                                 => "Project video (optional)",
        "t_project_images"                                         => "Project images",
        "t_ur_project_created_successfully"                        => "Your project has been successfully added",
        "t_about_this_project"                                     => "About this project",
        "t_share_on_facebook"                                      => "Share on Facebook",
        "t_share_on_twitter"                                       => "Share on Twitter",
        "t_share_on_linkedin"                                      => "Share on Linkedin",
        "t_share_on_snapchat"                                      => "Share on Snapchat",
        "t_share_on_pinterest"                                     => "Share on Pinterest",
        "t_share_on_whatsapp"                                      => "Share on WhatsApp",
        "t_share_this_project"                                     => "Share this project",
        "t_live_preview"                                           => "Live preview",
        "t_watch_video"                                            => "Watch video",
        "t_no_projects"                                            => "No projects",
        "t_this_user_has_no_projects_yet"                          => "This user has no projects right now.",
        "t_update_project"                                         => "Update project",
        "t_edit_project"                                           => "Edit project",
        "t_update_project_images"                                  => "Update project images",
        "t_ur_project_updated_successfully"                        => "Your project has been successfully updated",
        "t_hire_the_best_skill_name_experts"                       => "Hire the best :skill experts",
        "t_hire_the_best_skill_name_experts_subtitle"              => "Find the most talented :skill experts to bring your ideas to life",
        "t_block_user"                                             => "Block user",
        "t_are_u_sure_block_conversation_user"                     => "Are you sure you want to ban this user? you won't be able to undo this operation",
        "t_offline"                                                => "Offline",
        "t_list_of_latest_conversations"                           => "List of latest conversations",
        "t_no_messages_yet"                                        => "No messages yet",
        "t_no_messages_yet_subtitle"                               => "Looks like you haven't initiated a conversation with this user yet",
        "t_conversation_started_on_date"                           => "Conversation started on :date",
        "t_working_dots"                                           => "Working...",
        "t_view_more"                                              => "View more",
        "t_conversation_blocked_successfully"                      => "You have successfully blocked this conversation",
        "t_u_cant_reply_this_conversation"                         => "You can't reply this conversation!",
        "t_conversation_not_exists"                                => "This conversation does not exist",
        "t_no_conversation_selected"                               => "No conversation selected",
        "t_no_conversation_selected_subtitle"                      => "Please select a conversation to start messaging",
        "t_no_conversations"                                       => "No conversations",
        "t_u_dont_have_any_active_conversations"                   => "You don't have any active conversations yet",
        "t_search_results_for_q"                                   => "Search results for :q",
        "t_page_last_update_date"                                  => "Last update :date",
        "t_orders_in_queue"                                        => "Orders in queue",
        "t_posted_date"                                            => "Posted date",
        "t_starting_at"                                            => "Starting at",
        "t_u_cant_report_this_gig"                                 => "You can't report this gig",
        "t_read_faq_subtitle"                                      => "Read frequently asked questions",
        "t_click_to_view_reviews"                                  => "Click to view reviews",
        "t_seller_wont_be_able_to_receive_orders_date"             => "Seller is away right now, and he won't be able to receive new orders until <span class='font-bold'>:date</span>",
        "t_seller_wont_be_able_to_receive_orders_date_no_html"     => "Seller is away right now, and he won't be able to receive new orders until :date",
        "t_u_cant_add_ur_own_gigs_to_shopping_cart"                => "You can't add your own gigs to shopping cart",
        "t_you_may_also_like"                                      => "You may also like",
        "t_u_may_also_like_the_following_gigs"                     => "You may also like the following gigs",
        "t_see_more"                                               => "See more",
        "t_view_all_reviews"                                       => "View all reviews",
        "t_customer_reviews"                                       => "Customer reviews",
        "t_based_on_number_reviews"                                => "Based on :number reviews",
        "t_quantity_number"                                        => "Quantity :number",
        "t_available_balance"                                      => "Available balance",
        "t_balance"                                                => "Balance",
        "t_u_dont_have_enough_money_to_checkout"                   => "You don\'t have enough credit to make checkout",
        "t_too_many_login_attempts_pls_try_after_seconds"          => "Too many login attempts, Please try again in <span id='countdown-seconds' class='font-black  tracking-widest'>:seconds</span> seconds",
        "t_work_completed"                                         => "Work completed",
        "t_download_files"                                         => "Download files",
        "t_always_scan_files_before_open_delivered_work"           => "Always scan files from threats before opening them",
        "t_conversation_with_seller"                               => "Conversation with seller",
        "t_communicate_with_seller_about_changes"                  => "Communicate with seller about changes or requests",
        "t_download_received_files_from_seller"                    => "Download received files from seller",
        "t_are_sure_completed_work_buyer"                          => "Please confirm that you have received the work. You won\'t be able to ask the seller for any changes after that",
        "t_order_has_been_successfully_canceled"                   => "Order item has been successfully canceled",
        "t_are_u_sure_u_want_to_cancel_service"                    => "Are you sure you want to cancel this serivce?",
        "t_are_u_sure_u_want_to_start_this_order_seller"           => "This order status will be marked as in progress",
        "t_get_started"                                            => "Get started",
        "t_order_has_been_successfully_marked_progress"            => "Order has been successfully marked as in progress",
        "t_order_status"                                           => "Order status",
        "t_service_upgrades"                                       => "Service upgrades",
        "t_required_info"                                          => "Required information",
        "t_information_u_requested_from_buyer"                     => "List of information you requested from the buyer below",
        "t_no_upgrades_selected"                                   => "No upgrades selected",
        "t_buyer_didnt_selected_any_upgrades_or_no_upgrades"       => "Buyer did not select any upgrades or gig does not have one",
        "t_buyer"                                                  => "Buyer",
        "t_contact"                                                => "Contact",
        "t_gig_details"                                            => "Gig details",
        "t_process_the_order"                                      => "Process the order",
        "t_order_item_could_not_be_found"                          => "Order item could not be found",
        "t_write_a_review"                                         => "Write a review",
        "t_give_ur_opinion_about_this_item"                        => "Give your opinion about this item",
        "t_orders"                                                 => "Orders",
        "t_levels"                                                 => "Levels",
        "t_services"                                               => "Services",
        "t_stripe"                                                 => "Stripe",
        "t_general_settings"                                       => "General settings",
        "t_pages"                                                  => "Pages",
        "t_reports"                                                => "Reports",
        "t_conversations"                                          => "Conversations",
        "t_countries"                                              => "Countries",
        "t_portfolios"                                             => "Portfolios",
        "t_recaptcha"                                              => "reCaptcha",
        "t_general_settings_sidebar"                               => "General",
        "t_currency_settings_sidebar"                              => "Currency",
        "t_auth_settings_sidebar"                                  => "Authentication",
        "t_commission_settings_sidebar"                            => "Commission",
        "t_footer_settings_sidebar"                                => "Footer",
        "t_gateways_settings_sidebar"                              => "Payment gateways",
        "t_media_settings_sidebar"                                 => "Media",
        "t_publish_settings_sidebar"                               => "Publish",
        "t_security_settings_sidebar"                              => "Security",
        "t_withdrawal_settings_sidebar"                            => "Withdrawal",
        "t_total_income_from_orders_including_pending"             => "Net income including pending orders",
        "t_income_from_taxes"                                      => "Income from taxes",
        "t_taxes"                                                  => "Taxes",
        "t_income_from_commission"                                 => "Income from commission",
        "t_commission"                                             => "Commission",
        "t_withdrawn_money"                                        => "Withdrawn money",
        "t_approved_withdrawn_money_by_users"                      => "Approved withdrawn money by users",
        "t_total_success_sales"                                    => "Total successful sales",
        "t_total_gigs_including_pending"                           => "Total gigs including pending",
        "t_total_gigs"                                             => "Total gigs",
        "t_total_users_including_pending"                          => "Total users including pending",
        "t_total_users"                                            => "Total users",
        "t_total_messages"                                         => "Total messages",
        "t_total_messages_between_users"                           => "Total messages between users",
        "t_latest_visits_to_ur_website"                            => "Latest visits to your website",
        "t_latest_users"                                           => "Latest users",
        "t_latest_10_users"                                        => "Latest 10 users",
        "t_general_settings_subtitle"                              => "Change your site general settings",
        "t_site_title"                                             => "Site title",
        "t_enter_site_title"                                       => "Enter site title",
        "t_enter_site_title_separator"                             => "Enter title separator",
        "t_site_title_separator"                                   => "Title separator",
        "t_site_subtitle"                                          => "Site subtitle",
        "t_enter_site_subtitle"                                    => "Enter site subtitle",
        "t_site_logo"                                              => "Site logo",
        "t_site_favicon"                                           => "Site favicon",
        "t_top_navbar_announce_text"                               => "Top navbar announce text",
        "t_enter_top_navbar_announce_text"                         => "Enter top navbar announce text",
        "t_top_navbar_announce_link"                               => "Top navbar announce link",
        "t_enter_top_navbar_announce_link"                         => "Enter top navbar announce link",
        "t_language_switcher"                                      => "Language switcher",
        "t_enable_language_switcher"                               => "Enable language switcher",
        "t_enabled"                                                => "Enabled",
        "t_disabled"                                               => "Disabled",
        "t_default_language"                                       => "Default language",
        "t_choose_default_language"                                => "Choose default language",
        "t_auth_settings"                                          => "Auth settings",
        "t_auth_settings_subtitle"                                 => "Change your authentication settings",
        "t_verification_required"                                  => "Verification required",
        "t_verification_type"                                      => "Verification type",
        "t_choose_type_of_verification"                            => "Choose verification type",
        "t_from_dashboard"                                         => "From dashboard",
        "t_by_email_address"                                       => "By e-mail address",
        "t_verification_expiry_period"                             => "Verification expiry period",
        "t_verification_expiry_period_after_minutes"               => "Verification link will expire after how many minutes",
        "t_password_reset_expiry_period"                           => "Password reset expiry period",
        "t_password_expiry_period_after_minutes"                   => "Password reset link will expire after how many minutes",
        "t_enable_facebook_login"                                  => "Enable Facebook login",
        "t_facebook_client_id"                                     => "Facebook client id",
        "t_enter_facebook_client_id"                               => "Enter Facebook client id",
        "t_facebook_client_secret"                                 => "Facebook client secret",
        "t_enter_facebook_client_secret"                           => "Enter Facebook client secret",
        "t_enable_twitter_login"                                   => "Enable Twitter login",
        "t_twitter_client_id"                                      => "Twitter client id",
        "t_enter_twitter_client_id"                                => "Enter Twitter client id",
        "t_twitter_client_secret"                                  => "Twitter client secret",
        "t_enter_twitter_client_secret"                            => "Enter Twitter client secret",
        "t_enable_google_login"                                    => "Enable Google login",
        "t_google_client_id"                                       => "Google client id",
        "t_enter_google_client_id"                                 => "Enter Google client id",
        "t_google_client_secret"                                   => "Google client secret",
        "t_enter_google_client_secret"                             => "Enter Google client secret",
        "t_enable_github_login"                                    => "Enable Github login",
        "t_github_client_id"                                       => "Github client id",
        "t_enter_github_client_id"                                 => "Enter Github client id",
        "t_github_client_secret"                                   => "Github client secret",
        "t_enter_github_client_secret"                             => "Enter Github client secret",
        "t_enable_linkedin_login"                                  => "Enable Linkedin login",
        "t_linkedin_client_id"                                     => "Linkedin client id",
        "t_enter_linkedin_client_id"                               => "Enter Linkedin client id",
        "t_linkedin_client_secret"                                 => "Linkedin client secret",
        "t_enter_linkedin_client_secret"                           => "Enter Linkedin client secret",
        "t_commission_settings"                                    => "Commission settings",
        "t_commission_settings_subtitle"                           => "Update your commission settings",
        "t_enable_taxes"                                           => "Enable taxes",
        "t_fixed_amount"                                           => "Fixed amount",
        "t_percentage_amount"                                      => "Percentage amount",
        "t_tax_value"                                              => "Tax value",
        "t_enter_tax_value"                                        => "Enter tax value",
        "t_take_commission_from"                                   => "Take commission from",
        "t_withdrawal"                                             => "Withdrawal",
        "t_commission_type"                                        => "Commission type",
        "t_choose_commission_type"                                 => "Choose commission type",
        "t_commission_value"                                       => "Commission value",
        "t_enter_commission_value"                                 => "Enter commission value",
        "t_tax_type"                                               => "Tax type",
        "t_terms_page"                                             => "Terms page",
        "t_choose_terms_page"                                      => "Choose terms page",
        "t_privacy_policy_page"                                    => "Privacy policy page",
        "t_choose_privacy_policy_page"                             => "Choose privacy policy page",
        "t_footer_logo"                                            => "Footer logo",
        "t_copyrights"                                             => "Copyrights",
        "t_enter_copyrights_html_supported"                        => "Enter copyrights (html tags supported)",
        "t_facebook_page"                                          => "Facebook page",
        "t_enter_facebook_page_link"                               => "Enter Facebook page link",
        "t_twitter_page"                                           => "Twitter page",
        "t_enter_twitter_page_link"                                => "Enter Twitter page link",
        "t_instagram_page"                                         => "Instagram page",
        "t_enter_instagram_page_link"                              => "Enter Instagram page link",
        "t_linkedin_page"                                          => "Linkedin page",
        "t_enter_linkedin_page_link"                               => "Enter Linkedin page link",
        "t_pinterest_page"                                         => "Pinterest page",
        "t_enter_pinterest_page_link"                              => "Enter Pinterest page link",
        "t_youtube_page"                                           => "Youtube page",
        "t_enter_youtube_page_link"                                => "Enter Youtube page link",
        "t_github_page"                                            => "Github page",
        "t_enter_github_page_link"                                 => "Enter Github page link",
        "t_footer_settings"                                        => "Footer settings",
        "t_footer_settings_subtitle"                               => "Update your footer settings",
        "t_payment_gateways_settings"                              => "Payment gateways settings",
        "t_payment_gateways_settings_subtitle"                     => "If you want to add a new payment gateway, please contact us",
        "t_enable_paypal_gateway"                                  => "Enable PayPal gateway",
        "t_enable_stripe_gateway"                                  => "Enable Stripe gateway",
        "t_media_settings"                                         => "Media settings",
        "t_media_settings_subtitle"                                => "Change media settings",
        "t_required_files_max_size"                                => "Required files maximum size (MB)",
        "t_enter_required_files_max_size"                          => "Enter max size in MB for requirements files",
        "t_required_files_allowed_exts"                            => "Required files allowed extensions",
        "t_enter_required_files_allowed_exts"                      => "Enter allowed extensions for required files after purchase a gig",
        "t_delivered_work_max_size"                                => "Delivered work maximum size",
        "t_enter_delivered_work_max_size"                          => "Enter delivered work max size (MB)",
        "t_portfolio_max_images"                                   => "Portfolio maximum images",
        "t_enter_max_img_for_portfolios"                           => "Enter maximum images for portfolios",
        "t_portfolio_image_max_size"                               => "Portfolio image maximum size",
        "t_enter_portfolio_max_size_mb"                            => "Enter portfolio maximum size (MB)",
        "t_publish_settings"                                       => "Publish settings",
        "t_publish_settings_subtitle"                              => "Change publishing settings",
        "t_auto_approve_gigs"                                      => "Auto approve gigs",
        "t_max_tags"                                               => "Maximum tags",
        "t_enter_max_tags"                                         => "Enter maximum tags in a gig",
        "t_enable_videos_for_gigs"                                 => "Enable videos for gigs",
        "t_allow_documents_in_gigs"                                => "Allow upload documents for gig",
        "t_max_documents"                                          => "Maximum documents",
        "t_enter_max_docs_allowed"                                 => "Enter max documents allowed",
        "t_max_document_size"                                      => "Maximum document size",
        "t_enter_max_size_per_document_mb"                         => "Enter max size per document (MB)",
        "t_max_images_per_gig"                                     => "Maximum images per gig",
        "t_enter_max_images_per_gig"                               => "Enter max images per gig",
        "t_max_size_per_image"                                     => "Maximum size per image",
        "t_enter_max_size_per_image"                               => "Enter max size per image",
        "t_security_settings"                                      => "Security settings",
        "t_security_settings_subtitle"                             => "Update your security settings",
        "t_enable_recaptcha"                                       => "Enable reCaptcha",
        "t_withdrawal_settings"                                    => "Withdrawal settings",
        "t_withdrawal_settings_subtitle"                           => "Change withdrawal settings",
        "t_min_withdrawal_amount"                                  => "Minimum withdrawal amount",
        "t_enter_min_withdrawal_amount"                            => "Enter minimum withdrawal amount",
        "t_withdrawal_period"                                      => "Withdrawal period",
        "t_choose_withdrawal_system"                               => "Choose withdrawal system",
        "t_daily"                                                  => "Daily",
        "t_weekly"                                                 => "Weekly",
        "t_monthly"                                                => "Monthly",
        "t_update_paypal_settings"                                 => "Update PayPal settings",
        "t_default_mode"                                           => "Default mode",
        "t_choose_default_mode"                                    => "Choose default mode",
        "t_sandbox"                                                => "Sandbox",
        "t_live"                                                   => "Live",
        "t_paypal_client_id"                                       => "PayPal client id",
        "t_enter_paypal_client_id"                                 => "Enter PayPal client id",
        "t_paypal_client_secret"                                   => "PayPal client secret",
        "t_enter_paypal_client_secret"                             => "Enter PayPal client secret",
        "t_stripe_secret_key"                                      => "Stripe secret key",
        "t_enter_stripe_secret_key"                                => "Enter Stripe secret key",
        "t_update_stripe_settings"                                 => "Update Stripe settings",
        "t_update_recaptcha_settings"                              => "Update reCaptcha settings",
        "t_site_key"                                               => "Site key",
        "t_enter_site_key"                                         => "Enter site key",
        "t_secret_key"                                             => "Secret key",
        "t_enter_secret_key"                                       => "Enter secret key",
        "t_inactive"                                               => "Inactive",
        "t_edit_country"                                           => "Edit country",
        "t_create_country"                                         => "Create country",
        "t_country_name"                                           => "Country name",
        "t_enter_country_name"                                     => "Enter country name",
        "t_country_code"                                           => "Country code",
        "t_enter_country_code"                                     => "Enter country code",
        "t_country_status_active"                                  => "Country status active",
        "t_are_u_sure_u_want_to_delete_this"                       => "Are you sure you want to delete this?",
        "t_edit_page"                                              => "Edit page",
        "t_delete_page"                                            => "Delete page",
        "t_slug"                                                   => "Slug",
        "t_enter_title"                                            => "Enter title",
        "t_enter_slug"                                             => "Enter slug",
        "t_enter_page_content"                                     => "Enter page content",
        "t_content"                                                => "Content",
        "t_external_link"                                          => "External link",
        "t_enter_external_link"                                    => "Enter external link",
        "t_link"                                                   => "Link",
        "t_create_page"                                            => "Create page",
        "t_footer_column"                                          => "Footer column",
        "t_choose_footer_column"                                   => "Choose footer column",
        "t_code"                                                   => "Code",
        "t_rtl"                                                    => "RTL",
        "t_this_language_is_default_for_ur_website"                => "This language is the default language for your website",
        "t_language_name"                                          => "Language name",
        "t_enter_language_name"                                    => "Enter language name",
        "t_enable_this_language"                                   => "Enable this language",
        "t_force_rtl_for_this_language"                            => "Force rtl for this language",
        "t_edit_translation"                                       => "Edit translation",
        "t_language_code"                                          => "Language code",
        "t_enter_language_code"                                    => "Enter language code",
        "t_edit_translation_for_lang_name"                         => "Edit translation file for :lang",
        "t_language_key"                                           => "Key",
        "t_language_value"                                         => "Value",
        "t_search_words"                                           => "Search words...",
        "t_language_value_updated_successfully"                    => "Translation value has been successfully updated",
        "t_ip_lookup"                                              => "Ip lookup",
        "t_read_message"                                           => "Read message",
        "t_reply_message"                                          => "Reply message",
        "t_delete_message"                                         => "Delete message",
        "t_replied"                                                => "Replied",
        "t_seen"                                                   => "Seen",
        "t_new"                                                    => "New",
        "t_reply"                                                  => "Reply",
        "t_enter_ur_reply_here"                                    => "Enter your reply here",
        "t_send_reply"                                             => "Send reply",
        "t_message_reply_sent_successfully"                        => "Your reply has been successfully sent",
        "t_message_has_been_deleted"                               => "Message has been successfully deleted",
        "t_archived"                                               => "Archived",
        "t_blocked"                                                => "Blocked",
        "t_read_messages"                                          => "Read messages",
        "t_sender"                                                 => "Sender",
        "t_receiver"                                               => "Receiver",
        "t_reported_users"                                         => "Reported users",
        "t_reported_by"                                            => "Reported by",
        "t_are_u_sure_u_want_to_ban_this_user"                     => "Are you sure you want to ban this user?",
        "t_ban_user"                                               => "Ban user",
        "t_delete_report"                                          => "Delete report",
        "t_user"                                                   => "User",
        "t_user_has_been_banned_success"                           => "User has been successfully banned",
        "t_report_has_been_successfully_deleted"                   => "Report has been successfully deleted",
        "t_reported_gigs"                                          => "Reported gigs",
        "t_this_category_has_some_gigs_please_edit_it"             => "There are some gigs in this category, please try to edit it instead",
        "t_this_subcategory_has_some_gigs_please_edit_it"          => "There are some gigs in this subcategory, please try to edit it instead",
        "t_delete_category"                                        => "Delete category",
        "t_delete_subcategory"                                     => "Delete subcategory",
        "t_delete_review"                                          => "Delete review",
        "t_seller"                                                 => "Seller",
        "t_item"                                                   => "Item",
        "t_delete_portfolio"                                       => "Delete portfolio",
        "t_activate_portfolio"                                     => "Activate portfolio",
        "t_portfolio_published_successfully"                       => "Portfolio has been successfully published",
        "t_portfolio_deleted_successfully"                         => "Portfolio has been successfully deleted",
        "t_account_type"                                           => "Account type",
        "t_registeration_date"                                     => "Registeration date",
        "t_banned"                                                 => "Banned",
        "t_verified"                                               => "Verified",
        "t_edit_user"                                              => "Edit user",
        "t_user_details"                                           => "User details",
        "t_are_u_sure_u_want_to_delete_this_user"                  => "Are you sure you want to delete this user",
        "t_delete_user"                                            => "Delete user",
        "t_activate_user"                                          => "Activate user",
        "t_user_has_been_activated_success"                        => "User has been successfully activated",
        "t_create_user"                                            => "Create user",
        "t_choose_account_type"                                    => "Choose account type",
        "t_choose_country"                                         => "Choose country",
        "t_choose_level"                                           => "Choose level",
        "t_choose_status"                                          => "Choose status",
        "t_enter_available_credit"                                 => "Enter available credit",
        "t_selected_level_not_valid_for_account_type"              => "Selected level is not valid this account type",
        "t_account_has_been_created"                               => "Account has been successfully created",
        "t_upload_avatar"                                          => "Upload avatar",
        "t_email_verified_at"                                      => "Email verified at",
        "t_registeration_method"                                   => "Registeration method",
        "t_net_balance"                                            => "Net balance",
        "t_last_activity"                                          => "Last activity",
        "t_seller_sales"                                           => "Seller sales",
        "t_buyer_purchases"                                        => "Buyer purchases",
        "t_max_seller_sales_number"                                => "Max: :max",
        "t_min_seller_sales_number"                                => "Min: :min",
        "t_max_buyer_purchases_number"                             => "Max: :max",
        "t_min_buyer_purchases_number"                             => "Min: :min",
        "t_created_at"                                             => "Created at",
        "t_edit_level"                                             => "Edit level",
        "t_are_u_sure_u_want_to_delete_this_level"                 => "Are you sure you want to delete this level",
        "t_delete_level"                                           => "Delete level",
        "t_create_level"                                           => "Create level",
        "t_seller_maximum_sales"                                   => "Seller maximum sales",
        "t_seller_minimum_sales"                                   => "Seller minimum sales",
        "t_enter_maximum_sales_for_seller"                         => "Enter maximum sales for seller",
        "t_enter_minimum_sales_for_seller"                         => "Enter minimum sales for seller",
        "t_buyer_maximum_purchases"                                => "Buyer maximum purchases",
        "t_buyer_minimum_purchases"                                => "Buyer minimum purchases",
        "t_enter_maximum_purchases_for_buyer"                      => "Enter maximum purchases for buyer",
        "t_enter_minimum_purchases_for_buyer"                      => "Enter minimum purchases for buyer",
        "t_level_color"                                            => "Level color",
        "t_enter_color_hex_color_example"                          => "Enter color hex color e.g #FF0000",
        "t_level_created_successfully"                             => "Level has been successfully created",
        "t_level_updated_successfully"                             => "Level has been successfully updated",
        "t_u_cannot_delete_1_2_levels"                             => "You cannot delete this level, because we use it as default",
        "t_there_are_users_have_this_level"                        => "You cannot delete this level because there are users have it",
        "t_level_deleted_successfully"                             => "Level has been successfully deleted",
        "t_id"                                                     => "ID",
        "t_placed_at"                                              => "Placed at",
        "t_invoice_details"                                        => "Invoice details",
        "t_payment_method"                                         => "Payment method",
        "t_user_credit"                                            => "User credit",
        "t_payment_id"                                             => "Payment id",
        "t_seo_settings_sidebar"                                   => "Seo",
        "t_seo_settings"                                           => "SEO settings",
        "t_seo_settings_subtitle"                                  => "Update you website seo settings, you can find sitemap by adding /sitemap.xml to the end of your website link",
        "t_facebook_app_id"                                        => "Facebook app id",
        "t_enter_facebook_app_id"                                  => "Enter Facebook app id",
        "t_facebook_page_id"                                       => "Facebook page id",
        "t_enter_facebook_page_id"                                 => "Enter Facebook page id",
        "t_twitter_username"                                       => "Twitter username",
        "t_enter_twitter_username"                                 => "Enter Twitter username",
        "t_social_media_ogimage"                                   => "Social media og image",
        "t_enable_sitemap"                                         => "Enable sitemap",
        "t_smtp_settings_sidebar"                                  => "Smtp",
        "t_test_settings"                                          => "Test settings",
        "t_test_settings_smtp_subtitle"                            => "Type an email address to test your smtp settings",
        "t_smtp_email_test_sent_success"                           => "You will receive an email shortly. Otherwise you need to change your settings again",
        "t_smtp_host"                                              => "Smtp host",
        "t_enter_smtp_host"                                        => "Enter smtp host",
        "t_smtp_port"                                              => "Smtp port",
        "t_enter_smtp_port"                                        => "Enter smtp port",
        "t_smtp_encryption"                                        => "Smtp encryption",
        "t_choose_smtp_encryption"                                 => "Choose smtp encryption",
        "t_ssl"                                                    => "SSL",
        "t_tls"                                                    => "TLS",
        "t_smtp_from_address"                                      => "From address",
        "t_enter_your_company_website_name"                        => "Enter your company or website name",
        "t_smtp_from_name"                                         => "From name",
        "t_smtp_settings"                                          => "Smtp settings",
        "t_smtp_settings_subtitle"                                 => "Set your smtp settings, to send notifications and emails to users",
        "t_verifications"                                          => "Verifications",
        "t_document_type"                                          => "Document type",
        "t_front_side"                                             => "Front side",
        "t_back_side"                                              => "Back side",
        "t_approve_files"                                          => "Approve files",
        "t_are_u_sure_u_want_to_approve_this_verification"         => "Are you sure you want to approve selected files?",
        "t_decline_files"                                          => "Decline files",
        "t_are_u_sure_u_want_to_decline_this_verification"         => "Are you sure you want to decline selected files?",
        "t_declined"                                               => "Declined",
        "t_approve_payment"                                        => "Approve payment",
        "t_reject_payment"                                         => "Reject payment",
        "t_are_u_sure_u_want_to_approve_this_withdrawal"           => "Please remember that you have to send money manually to this user before you can approve the payment",
        "t_are_u_sure_u_want_to_reject_this_withdrawal"            => "Are you sure you want to reject this payment?",
        "t_withdrawal_manually_message_alert"                      => "In current version, you will have to send payments manually. Until next updates, you will be able to config how you want to send payments, either from dashboard or automatically. Thank you",
        "t_payment_approved_successfully"                          => "Payment has been successfully approved",
        "t_in_queue"                                               => "In queue",
        "t_delete_gig"                                             => "Delete gig",
        "t_edit_gig_basic_details"                                 => "Edit basic gig details",
        "t_overview_section_has_been_saved"                        => "Overview section has been successfully saved",
        "t_pricing_section_has_been_saved"                         => "Pricing section has been successfully saved",
        "t_requirements_section_has_been_saved"                    => "Requirements section has been successfully saved",
        "t_gig_gallery"                                            => "Gig gallery",
        "t_if_u_upload_new_images_below_will_be_deleted"           => "If you upload new images, the existing below will be erased",
        "t_update_gig_gallery_documents_and_video"                 => "Update gig images, documents and video",
        "t_are_u_sure_delete_this_image"                           => "Are you sure you want to delete this image?",
        "t_please_wait_dots"                                       => "Please wait...",
        "t_file_has_been_successfully_deleted"                     => "File has been successfully deleted",
        "t_gig_thumbnail"                                          => "Gig thumbnail",
        "t_catch_buyers_eyes_with_nice_img"                        => "Catch buyers eyes with a nice image",
        "t_upload_thumbnail"                                       => "Upload thumbnail",
        "t_are_u_sure_delete_this_file"                            => "Are you sure you want to delete this file?",
        "t_gig_updated_successfull"                                => "Gig has been successfully updated",
        "t_update_gig_details_seo_faqs"                            => "Update gig details, seo and frequently asked questions",
        "t_edit_gig_price_upgrades"                                => "Edit gig price and set or update upgrades",
        "t_set_required_info_to_get_from_buyer"                    => "Set the required info the buyer will provide after buy this gig",
        "t_u_cant_delete_this_gig_pending_orders"                  => "You cannot delete this gig, because it has orders in queue",
        "t_gig_deleted_successfull"                                => "Gig has been successfully deleted",
        "t_pending_gigs"                                           => "Pending gigs",
        "t_pending_verifications"                                  => "Pending verifications",
        "t_pending_withdrawals"                                    => "Pending withdrawals",
        "t_pending_portfolios"                                     => "Pending portfolios",
        "t_pending_users"                                          => "Pending users",
        "t_pending_refunds"                                        => "Pending refunds",
        "t_go_homepage"                                            => "Go homepage",
        "t_fast_support"                                           => "Fast support", 
        "t_current_password_doesnt_match"                          => "Current password does not match our records",
        "t_admin_profile_updated"                                  => "Your account settings has been successfully updated",
        "t_admin_account_settings_subtitle"                        => "Update your account settings. Current password is required to do this action",
        "t_conversation_messages"                                  => "Conversation messages",
        "t_edit_gallery"                                           => "Edit gallery",
        "t_edit_overview"                                          => "Edit overview",
        "t_edit_pricing"                                           => "Edit pricing",
        "t_edit_requirements"                                      => "Edit requirements",
        "t_create_language"                                        => "Create language",
        "t_newsletter"                                             => "Newsletter",
        "t_newsletter_settings"                                    => "Newsletter settings",
        "t_pls_select_export_type"                                 => "You can only import all, pending or verified list",
        "t_newsletter_settings_subtitle"                           => "Enable or disable newsletter",
        "t_enable_newsletter_system"                               => "Enable newsletter system",
        "t_newsletter_emails"                                      => "Newsletter emails",
        "t_ip_address"                                             => "Ip address",
        "t_resend_verification"                                    => "Resend verification",
        "t_delete_email"                                           => "Delete email",
        "t_a_verification_link_sent_to_this_email"                 => "A verification link has been successfully sent to this email address",
        "t_email_newsletter_deleted_success"                       => "Email address has been successfully deleted from list",
        "t_export_all"                                             => "Export all",
        "t_export_pending"                                         => "Export pending",
        "t_export_verified"                                        => "Export verified",
        "t_my_messages"                                            => "My messages",
        "t_favorite_list"                                          => "Favorite list",
        "t_my_reviews"                                             => "My reviews",
        "t_account_reviews_subtitle"                               => "Your reviews for purchased gigs, you can edit or delete them",
        "t_are_u_sure_u_want_to_delete_this_review"                => "Are you sure you want to delete this review?",
        "t_preview_review"                                         => "Preview review",
        "t_edit_review"                                            => "Edit review",
        "t_here_is_how_ur_review_looks_like"                       => "Here is how your review for this gig looks like",
        "t_update_ur_review_for_this_gig"                          => "Update your review for this gig",
        "t_enter_your_review_message"                              => "Enter your review message",
        "t_review_updated_succes"                                  => "Your review for this gig has been successfully changed",
        "t_review_deleted_successfully"                            => "Review has been successfully deleted",
        "t_favorite_list_subtitle"                                 => "Recently added to your favorite list",
        "t_are_u_sure_u_want_to_remove_from_favorite_list"         => "Are you sure you want to remove this gig from favorite list?",
        "t_billing_info_updated_success"                           => "Your billing information has been successfully updated",
        "t_billing_information_subtitle"                           => "Update your billing information below",
        "t_vat_number"                                             => "VAT number",
        "t_enter_vat_number_optional"                              => "Enter VAT number (optional)",
        "t_city"                                                   => "City",
        "t_enter_city"                                             => "Enter city",
        "t_zip"                                                    => "postal code",
        "t_enter_zip"                                              => "Enter postal code",
        "t_upload_gig_images"                                      => "Upload gig images",
        "t_this_gig_not_activated_yet"                             => "This gig is under review now, and it will be publicly visible soon",
        "t_sign_up_for_newsletter"                                 => "Sign up for our newsletter",
        "t_sign_up_for_newsletter_subtitle"                        => "Sign up for our weekly update and be the first to know about our specials and promotions",
        "t_pls_enter_valid_email_address"                          => "Please enter a valid email address",
        "t_we_sent_verification_link_newsletter"                   => "We have sent you a verification link to your email to confirm your email address",
        "t_get_started_today"                                      => "Get Started Today",
        "t_get_ur_work_done_easy_safe"                             => "Get your work done easily and safely",
        "t_are_u_sure_u_want_publish_this_gig"                     => "Are you sure you want to publish this gig?",
        "t_publish_gig"                                            => "Publish gig",
        "t_gig_published_success"                                  => "Gig has been successfully published",
        "t_my_refunds"                                             => "My refunds",
        "t_u_cant_request_refund_for_this_item_now"                => "You cannot request refund for this item",
        "t_u_can_request_refund_when_expected_date_finish"         => "You can request a refund for this item when expected delivery date expires",
        "t_request_refund_subtitle"                                => "We recommend you always to contact the seller before request a refund",
        "t_enter_refund_reason"                                    => "Please describe your issue in details",
        "t_item_details"                                           => "Item details",
        "t_item_details_subtitle"                                  => "More details about your item",
        "t_expected_delivery_date_on_date"                         => "Delivery expected on :date",
        "t_order_placed"                                           => "Order placed",
        "t_processing"                                             => "Processing",
        "t_refund_details"                                         => "Refund details",
        "t_refund_details_subtitle"                                => "Details and conversation about your refund",
        "t_enter_your_message"                                     => "Enter your message",
        "t_no_messages_in_this_refund_conversation"                => "This conversation still has no message",
        "t_refund_msg_posted_success"                              => "Your message has been successfully sent",
        "t_conversation"                                           => "Conversation",
        "t_are_u_sure_close_refund_dispute"                        => "Are you sure you want to close this refund dispute?",
        "t_close_refund"                                           => "Close refund",
        "t_raise_a_dispute"                                        => "Raise a dispute",
        "t_are_u_sure_raise_dispute_refund"                        => "Are you sure you want to raise a dispute to have :app_name investigate this refund request",
        "t_in_progress"                                            => "In progress",
        "t_declined_by_seller"                                     => "Declined by seller",
        "t_declined_by_admin"                                      => "Declined by :app_name",
        "t_approved_by_seller"                                     => "Approved by seller",
        "t_approved_by_admin"                                      => "Approved by :app_name",
        "t_closed"                                                 => "Closed",
        "t_created_on_date"                                        => "Created on :date",
        "t_u_have_closed_refund_success"                           => "You have successfully closed this refund dispute",
        "t_raise_dispute_request_received_success"                 => "Your request has been successfully received",
        "t_our_team_investigate_refund_request_now"                => "Our team is currently investigating your refund request and we are going to give you a response as soon as possible",
        "t_refunds_subtitle"                                       => "Your latest refunds requests",
        "t_dispute_opened"                                         => "Dispute opened",
        "t_send_requirements_subtitle"                             => "Send the required info to seller to process your order",
        "t_order_item_will_mark_done_after_1_week"                 => "This order item will automatically be marked as completed after 1 week from the delivery date :date",
        "t_submit_ur_review"                                       => "Submit your review",
        "t_u_have_to_add_at_least_1_requirement"                   => "You have to add at least one required form in order to continue",
        "t_this_gig_has_orders_in_queue_delete"                    => "This gig has orders in queue, please finish them before you can delete it",
        "t_delete_project"                                         => "Delete project",
        "t_are_u_sure_u_want_to_delete_gig"                        => "Are you sure you want to delete this gig?",
        "t_are_u_sure_u_want_to_delete_project"                    => "Are you sure you want to delete this project from portfolio?",
        "t_auto_approve_user_portfolio"                            => "Auto approve users portfolio projects",
        "t_project_deleted_success"                                => "Project has been successfully deleted",
        "t_withdrawal_money_not_enough"                            => "You do not have this amount in your account, please try again",
        "t_back_to_refunds"                                        => "Back to refunds",
        "t_refund_status"                                          => "Refund status",
        "t_refund_date"                                            => "Refund date",
        "t_refund_reason"                                          => "Refund reason",
        "t_give_refund"                                            => "Give refund",
        "t_decline_refund"                                         => "Decline refund",
        "t_are_u_sure_u_want_to_give_refund"                       => "Are you sure you want to accept this refund?",
        "t_are_u_sure_u_want_to_decline_refund"                    => "Are you sure you want to decline this refund?",
        "t_u_have_approved_this_refund"                            => "You have successfully accepted this refund",
        "t_ur_gig_title_has_been_published"                        => "Your gig :title has been successfully published",
        "t_ur_portfolio_title_has_been_published"                  => "Your project :title has been successfully published",
        "t_ur_account_has_verified"                                => "Your account has been successfully verified",
        "t_verification_files_declined"                            => "Verification files has been declined",
        "t_withdrawal_amount_paid"                                 => "Your recent withdrawal has been approved",
        "t_withdrawal_amount_rejected"                             => "Your recent withdrawal has rejected",
        "t_buyer_has_canceled_order"                               => ":buyer has canceled an order",
        "t_buyer_sent_u_message_about_delivered_files"             => ":buyer sent you a message about delivered files",
        "t_order_id_completed"                                     => "Order :id has been completed",
        "t_new_message_about_refund"                               => "You have new message about a refund",
        "t_a_refund_has_closed"                                    => ":buyer has closed a refund",
        "t_buyer_opened_new_refund_dispute"                        => ":buyer has opened a new refund dispute",
        "t_u_have_received_new_rating"                             => "You have received a new rating",
        "t_u_became_a_seller"                                      => "Welcome! You have became a seller",
        "t_u_received_new_order_seller"                            => "You have received a new order",
        "t_u_have_new_message_from_username"                       => "You have new message from :username",
        "t_seller_has_canceled_ur_order"                           => ":seller has canceled your order",
        "t_seller_has_started_ur_order"                            => ":seller has started your order",
        "t_seller_has_delivered_ur_order"                          => ":seller has delivered your order",
        "t_seller_sent_u_msg_about_ur_refund_request"              => ":seller has sent you a message about your refund request",
        "t_seller_has_accepted_ur_refund"                          => ":seller has accepted your refund",
        "t_u_have_declined_this_refund"                            => "You have declined this refund",
        "t_seller_has_declined_ur_refund"                          => ":seller has declined your refund",
        "t_order_has_marked_as_completed"                          => "Order has marked as completed",
        "t_view"                                                   => "View",
        "t_mark_as_read"                                           => "Mark as read",
        "t_no_notification_right_now"                              => "No notification to show right now",
        "t_review_details"                                         => "Review details",
        "t_browse_parent_category"                                 => "Browse :category",
        "t_welcome"                                                => "Welcome",
        "t_regards"                                                => "Regards",
        "t_if_u_have_trouble_click_link_notification"              => "If you are having trouble clicking the :actionText button, copy and paste the URL below\n into your web browser:",
        "t_notification_user_everyone_welcome_1"                   => "With one click, you’ve opened the door to the world’s most talented freelancer marketplace",
        "t_notification_user_everyone_welcome_2"                   => "Finally, there’s a lifetime of opportunities in one place. All those projects you have planned or on hold? Just one click away. No need to prioritize, postpone, or procrastinate – thousands of skilled professionals are ready to take on your next project, from content and design to business and development. It’s time to start seeing those “to-do” tasks “done”",
        "t_start_exploring"                                        => "Start exploring",
        "t_welcome_to_the_future_of_work"                          => "Welcome to the future of work",
        "t_welcome_to_app_name"                                    => "Welcome to :name",
        "t_subject_admin_new_refund_message"                       => "New refund message",
        "t_subject_admin_pending_gig"                              => "New gig pending approval",
        "t_subject_admin_new_support_message"                      => "New support message",
        "t_subject_admin_pending_portfolio"                        => "New portfolio pending approval",
        "t_subject_admin_pending_user"                             => "New user pending approval",
        "t_subject_admin_pending_withdrawal"                       => "Pending withdrawal request",
        "t_subject_admin_profile_reported"                         => "Profile reported",
        "t_subject_admin_refund_dispute_raised"                    => "Refudn dispute raised",
        "t_hi_admin"                                               => "Hi admin!",
        "t_notification_admin_pending_gig"                         => "You have a new gigs pending approval, click the button below to see them.",
        "t_notification_admin_support_message"                     => "You have received the following message",
        "t_notification_admin_pending_portfolio"                   => "You have a new portfolio pending approval",
        "t_notification_admin_pending_user"                        => "New user just signed up and waiting for approval, you can wait until he verify his email address or approve his account from dashboard",
        "t_notification_admin_pending_withdrawal"                  => "You have new withdrawals pending approval, please check them in dashboard",
        "t_notification_admin_reported_profile"                    => "A user has reported a profile in your website, please click the button below to see reported profiles",
        "t_notification_admin_refund_dispute"                      => "A user has raised a dispute about a refund, because seller has decided to decline it, please click the button below to see refund details",
        "t_subject_buyer_new_refund_message"                       => "New message on your refund request",
        "t_subject_buyer_order_delivered"                          => "Your order item has been delivered",
        "t_subject_buyer_order_item_in_progress"                   => "Your order item in progress",
        "t_subject_buyer_order_canceled"                           => "Your order item has been canceled by seller",
        "t_subject_buyer_order_item_completed_thanks"              => "Your order item has been completed",
        "t_subject_buyer_order_has_placed"                         => "Your order has placed",
        "t_subject_buyer_refund_accepted"                          => "Your refund request has accepted",
        "t_subject_buyer_refund_declined"                          => "Your refund request has declined by seller",
        "t_hello_username"                                         => "Hello :username",
        "t_view_delivered_work"                                    => "View delivered work",
        "t_notification_ur_order_item_has_delivered"               => "Your order item has been successfully delivered by seller, click the button below to see delivered work",
        "t_notification_u_have_new_msg_refund_from_seller"         => "You have received this message from seller about your refund request, click the button below to see refund details",
        "t_notification_buyer_item_canceled"                       => "Seller has canceled your order item, click the button below to see your recent orders",
        "t_notification_buyer_item_completed"                      => "Thank you! your order item has been successfully delivered and completed",
        "t_notification_buyer_item_in_progress"                    => "Seller has started your order item and he will delivered to you as soon as possible",
        "t_notification_buyer_order_placed"                        => "Thank you! your order has been successfully placed do not forget to submit the required info for sellers to start your order items",
        "t_notification_buyer_refund_accepted"                     => "Seller has accepted your refund request",
        "t_notification_buyer_refund_declined"                     => "Seller has decided to decline your refund request, you still can raise a dispute to support to review your refund",
        "t_subject_everyone_ur_account_activated"                  => "Your account has been activated",
        "t_subject_everyone_billing_info_updated"                  => "Your billing info has updated",
        "t_subject_everyone_ur_gig_published"                      => "Your gig is now published",
        "t_subject_everyone_u_have_new_message"                    => "Your have new message",
        "t_subject_everyone_password_changed"                      => "Your password has changed",
        "t_subject_everyone_reset_ur_password"                     => "Reset your password",
        "t_subject_everyone_payment_approved"                      => "Your payment has approved",
        "t_subject_everyone_payment_rejected"                      => "Your payment has rejected",
        "t_subject_everyone_verification_approved"                 => "Your verification has approved",
        "t_subject_everyone_verification_declined"                 => "Your verification files has declined",
        "t_subject_everyone_verify_ur_email"                       => "Verify your email address",
        "t_notification_billing_info_updated"                      => "Your billing info has been successfully updated.",
        "t_notification_gig_published"                             => "Your gig has been successfully published, click the button below to see your it.",
        "t_view_conversation"                                      => "View conversation",
        "t_notification_u_received_new_message"                    => "You have received new message, click button below to see the conversation",
        "t_notification_ur_password_updated"                       => "Your password has successfully updated, if you did not do that action, please reset your password now",
        "t_notification_click_button_to_reset_password"            => "Click the button below to reset your password",
        "t_notification_everyone_payment_accepted"                 => "Your payment request has been successfully approved",
        "t_notification_everyone_payment_rejected"                 => "Your payment request has rejected, please contact us to get more details",
        "t_notification_verification_approved"                     => "Your verification request has been successfully approved, you received the verified badge on your account now",
        "t_notification_verification_declined"                     => "Your verification files could not be approved, please resubmit your files again",
        "t_verify_email"                                           => "Verify email address",
        "t_notification_click_btn_to_verify_email"                 => "Click the button below to verify your email address, if you did not sign up, just forget this message",
        "t_subject_seller_u_became_seller"                         => "Welcome to seller dashboard",
        "t_subject_seller_delivered_work_new_msg"                  => "New delivered work message from buyer",
        "t_subject_seller_new_refund_msg"                          => "New refund message",
        "t_subject_seller_order_item_canceled"                     => "Order has canceled",
        "t_subject_seller_order_item_completed"                    => "Order has completed",
        "t_subject_seller_pending_order"                           => "New order",
        "t_subject_seller_pending_withdrawal"                      => "Your withdrawal is pending approval",
        "t_subject_seller_portfolio_published"                     => "Your portfolio has been published",
        "t_subject_seller_refund_closed"                           => "Refund has closed",
        "t_subject_seller_refund_request"                          => "New refund request",
        "t_subject_seller_new_review"                              => "New review",
        "t_notification_seller_line_1_u_became_seller"             => "You have became a seller now, you can share your works now and start receiving orders",
        "t_notification_seller_line_1_review_received"             => "You just received a new review, click button below to see it.",
        "t_notification_seller_line_1_refund_request"              => "A buyer requested a refund for an item, click button below to see refund details",
        "t_notification_seller_line_1_refund_closed"               => "A buyer has closed a refund, click button below to see the refund details",
        "t_notification_seller_line_1_portfolio_published"         => "You portfolio is now public, click below to see your project online",
        "t_notification_seller_line_1_pending_withdrawal"          => "Your withdrawal request is pending approval now",
        "t_notification_seller_line_1_pending_order"               => "You just received a new order, click button below to see order details",
        "t_notification_seller_line_1_order_completed"             => "Order has completed successfully",
        "t_notification_seller_line_1_order_canceled"              => "Buyer has canceled an order item, click button below to see order details",
        "t_notification_seller_line_1_new_refund_msg"              => "You have a new message about a refund from the buyer, click button below to see refund details",
        "t_notification_seller_line_1_delivered_work_msg"          => "You have a new message about delivered work, click button below to see more details",
        "t_re_subject"                                             => "RE: :subject",
        "t_verify_ur_email"                                        => "Verify your email address",
        "t_mail_newsletter_verify_line"                            => "Thank you for subscribing to our newsletter, to complete your sign up, please verify your email address",
        "t_welcome_to_newsletter_tnx"                              => "Thank you for subscribing",
        "t_mail_newsletter_welcome_line"                           => "Thank you for joining our newsletter, We look forward to sharing actionable content you can use",
        "t_validator_required"                                     => "Field required",
        "t_validator_email"                                        => "Field must be a valid email address",
        "t_validator_max"                                          => "Maximum characters is :max",
        "t_validator_min"                                          => "Minimum characters is :min",
        "t_validator_unique"                                       => "Value has already been taken",
        "t_validator_image"                                        => "Field must be an image",
        "t_validator_mimes"                                        => "Invalid file extension",
        "t_validator_boolean"                                      => "Field must be true or false",
        "t_validator_in"                                           => "Invalid selected value",
        "t_validator_integer"                                      => "Field must be an integer",
        "t_validator_same"                                         => "Field does not match",
        "t_validator_exists"                                       => "Selected value is invalid",
        "t_validator_numeric"                                      => "Field must be a number",
        "t_validator_array"                                        => "Field must be must be an array",
        "t_validator_min_array"                                    => "Field must have at least :min items",
        "t_validator_max_array"                                    => "Field not have more than :max items",
        "t_validator_regex"                                        => "Field format is invalid",
        "t_validator_max_size"                                     => "File must not be greater than :max",
        "t_validator_date_format"                                  => "Invalid date format",
        "t_validator_url"                                          => "Field must be a valid URL",
        "t_validator_file"                                         => "Invalid file",
        "t_submit_required_info"                                   => "Submit required information",
        "t_submit_new_review"                                      => "Submit new review",
        "t_facebook_login"                                         => "Facebook login",
        "t_google_login"                                           => "Google login",
        "t_twitter_login"                                          => "Twitter login",
        "t_linkedin_login"                                         => "Linkedin login",
        "t_github_login"                                           => "Github login",
        "t_casdoor_login"                                          => "Casdoor login",
        "t_processing_login"                                       => "Processing login...",
        "t_request_verification_link"                              => "Request verification link",
        "t_username_portfolio"                                     => ":username portfolio",
        "t_this_is_why_buyer_requested_refund"                     => "This is why the buyer requested refund",
        "t_item_gig"                                               => "Item gig",
        "t_item_status"                                            => "Item status",
        "t_are_u_sure_u_want_to_approve_this_refund"               => "Are you sure you want to approve this refund?",
        "t_are_u_sure_u_want_to_decline_this_refund"               => "Are you sure you want to decline this refund?",
        "t_u_cant_do_action_for_this_refund"                       => "You cannot do any action for this refund right now",
        "t_app_name_has_approved_ur_refund_request"                => ":app_name has accepted your refund request",
        "t_app_name_has_approved_refund_request_from_buyer"        => ":app_name has accepted refund request from :username",
        "t_app_name_has_declined_ur_refund_request"                => ":app_name has declined your refund request",
        "t_advertisements"                                         => "Advertisements",
        "t_setup_ads"                                              => "Setup ads",
        "t_header_code"                                            => "Header code",
        "t_enter_ads_header_code"                                  => "This code will be before </head> tag, html is allowed",
        "t_enter_ad_code"                                          => "Enter ad code",
        "t_service_360_ad"                                         => "Gig page 360px ad",
        "t_service_720_ad"                                         => "Gig page 720px ad",
        "t_ads_has_updated_successfully"                           => "Advertisements has been successfully updated",
        "t_system_logs"                                            => "System logs",
        "t_page_not_fount"                                         => "Page not found",
        "t_pls_check_url_address_bar_try_again"                    => "Please check the URL in the address bar and try again",
        "t_unauthorized"                                           => "Unauthorized",
        "t_sorry_ur_request_could_not_be_processed_try_again"      => "sorry your request could not be processed. please try again later",
        "t_forbidden"                                              => "Forbidden",
        "t_u_dont_have_permissions_to_access_page"                 => "You do not have permission to access this page",
        "t_page_expired"                                           => "Page expired",
        "t_sorry_ur_session_expired_refresh"                       => "Sorry, your session has expired. Please refresh and try again",
        "t_too_many_requests"                                      => "Too many requests",
        "t_u_have_sent_too_many_requests_time"                     => "You have sent too many requests in a given amount of time",
        "t_internal_server_error"                                  => "Internal server error",
        "t_server_encountered_and_error_request"                   => "The server encountered an error and could not complete your request",
        "t_service_unavailable"                                    => "Service unavailable",
        "t_the_service_u_request_not_available_now"                => "The service you requested is not available at this time",
        "t_loading_dots"                                           => "Loading...",
        "t_learn_more"                                             => "Learn more",
        "t_help"                                                   => "Help",
        "t_featured_categories"                                    => "Featured categories",
        "t_expected_delivery_date_time"                            => ":date for delivery",
        "t_blog"                                                   => "Blog",
        "t_browse_articles"                                        => "Browse articles",
        "t_create_article"                                         => "Create article",
        "t_blog_settings"                                          => "Blog settings",
        "t_article_image"                                          => "Article image",
        "t_reading_time"                                           => "Reading time",
        "t_enter_reading_time_in_minutes"                          => "Enter reading time (Minutes)",
        "t_enter_article_content"                                  => "Enter article content",
        "t_enter_description_for_seo"                              => "Enter description for SEO",
        "t_edit_article"                                           => "Edit article",
        "t_enable_blog_system"                                     => "Enable blog system",
        "t_enable_comments_in_blog_articles"                       => "Enable comments in blog articles",
        "t_auto_approve_comments_in_articles"                      => "Auto approve comments in articles",
        "t_articles"                                               => "Articles",
        "t_article"                                                => "Article",
        "t_delete_article"                                         => "Delete article",
        "t_comments"                                               => "Comments",
        "t_reading_time_x_minute"                                  => ":time minute(s)",
        "t_are_u_sure_u_want_to_approve_this_comment"              => "Are you sure you want to approve this comment?",
        "t_approve_comment"                                        => "Approve comment",
        "t_delete_comment"                                         => "Delete comment",
        "t_are_u_sure_u_want_to_hide_this_comment"                 => "Are you sure you want to hide this comment?",
        "t_hide_comment"                                           => "Hide comment",
        "t_edit_and_view"                                          => "Edit & view",
        "t_hidden"                                                 => "Hidden",
        "t_edit_comment"                                           => "Edit comment",
        "t_comment"                                                => "Comment",
        "t_enter_comment_content"                                  => "Enter comment content",
        "t_x_min_read"                                             => ":x min read",
        "t_latest_appname_news"                                    => "All the latest :app_name news, straight from the team.",
        "t_enter_ur_comment"                                       => "Enter your comment",
        "t_add_comment"                                            => "Add comment",
        "t_by_add_comment_agree_terms_privacy"                     => "By adding comment, you agree to our <a href=':terms_link' class='font-medium text-gray-900 hover:underline'>:terms_text</a> and <a href=':privacy_link' class='font-medium text-gray-900 hover:underline'>:privacy_text</a>",
        "t_ur_comment_has_been_successfully_added"                 => "You comment has been successfully posted, Thank you!",
        "t_subject_admin_pending_article_comment"                  => "Pending article comment",
        "t_ur_comment_will_be_published_soon"                      => "You comment will be visible shortly, Thank you!",
        "t_appearance_settings_sidebar"                            => "Appearance",
        "t_appearance_settings"                                    => "Appearance settings",
        "t_appreance_settings_subtitle"                            => "Update your appearance settings",
        "t_home_hero_section_image"                                => "Home hero section image",
        "t_hero_section_badge_short_text"                          => "Hero section badge short text",
        "t_enter_text_here"                                        => "Enter text here",
        "t_hero_section_badge_long_text"                           => "Hero section badge long text",
        "t_enter_link"                                             => "Enter link",
        "t_primary_button_link"                                    => "Primary button link",
        "t_secondary_button_text"                                  => "Secondary button text",
        "t_secondary_button_link"                                  => "Secondary button link",
        "t_custom_hero_section_code"                               => "Custom hero section code",
        "t_use_ur_own_html_hero_section_code"                      => "You can put your own html code for hero section here",
        "t_show_featured_categories"                               => "Show featured categories",
        "t_best_sellers"                                           => "Best sellers",
        "t_show_best_sellers"                                      => "Show bestsellers",
        "t_primary_button_text"                                    => "Primary button text",
        "t_hero_section_badge_link"                                => "Badge link",
        "t_top_sellers"                                            => "Top sellers",
        "t_hire_our_best_sellers"                                  => "Hire our best experts sellers",
        "t_offline_payment"                                        => "Offline payment",
        "t_offline_payment_settings"                               => "Offline payment settings",
        "t_setup_alternative_payment_offline"                      => "Set up to arrange alternative payments that best suits your business and your customers",
        "t_enable_offline_payments"                                => "Enable offline payments",
        "t_payment_method_name"                                    => "Payment method name",
        "t_enter_payment_method_name"                              => "Enter payment method name",
        "t_enter_payment_method_details"                           => "Enter payment method details (HTML allowed)",
        "t_payment_method_not_enabled"                             => "Selected payment gateway is not enabled, Please try another one.",
        "t_pending_offline_payment"                                => "Pending offline payment",
        "t_notification_admin_pending_offline_payment"             => "A customer used offline payment in his order, Once you receive the payment mark the invoice as paid, so seller can process his order",
        "t_order_placed_waiting_offline_payment"                   => "You order has been placed, once we receive a payment you will be able to submit the required info to buyer",
        "t_pending_payment"                                        => "Pending payment",
        "t_we_are_waiting_for_payment_first"                       => "We are waiting for a payment first",
        "t_invoices"                                               => "Invoices",
        "t_are_u_sure_u_received_invocie_payment"                  => "Are you sure you received the payment for this invoice?",
        "t_payment_received"                                       => "Payment received",
        "t_ur_payment_has_been_received_offline"                   => "Your payment has been received, you can now submit the required files to seller",
        "t_paystack"                                               => "Paystack",
        "t_paystack_payment_settings"                              => "Paystack settings",
        "t_paystack_payment_settings_subtitle"                     => "This payment gateway supports most countries in Africa",
        "t_enable_this_payment_gateway"                            => "Enable this payment gateway",
        "t_enter_payment_gateway_description"                      => "Enter payment gateway description",
        "t_public_key"                                             => "Public key",
        "t_enter_public_key"                                       => "Enter public key",
        "t_merchant_email_address"                                 => "Merchant email address",
        "t_cashfree_payment_settings"                              => "Cashfree settings",
        "t_cashfree"                                               => "Cashfree",
        "t_cashfree_payment_settings_subtitle"                     => "This payment gateway supports India",
        "t_environment"                                            => "Environment",
        "t_choose_environment"                                     => "Choose environment",
        "t_app_id"                                                 => "App id",
        "t_enter_app_id"                                           => "Enter app id",
        "t_xendit_payment_settings"                                => "Xendit settings",
        "t_xendit_payment_settings_subtitle"                       => "Accept payments in Indonesia and Philippines with Xendit",
        "t_xendit"                                                 => "Xendit",
        "t_select_payment_method"                                  => "Select a payment method",
        "t_selected"                                               => "Selected",
        "t_phone_number"                                           => "Phone number",
        "t_enter_ur_phone_number"                                  => "Enter your phone number",
        "t_pls_insert_a_valid_phone_number"                        => "Please insert a valid phone number",
        "t_error_cashfree_payment_failed"                          => "We were unable to handle your Cashfree payment. Please try again",
        "t_pls_insert_a_valid_cc_info"                             => "Please insert a valid credit card",
        "t_default_currency"                                       => "Default currency",
        "t_enter_exchange_rate_to_ur_default_currency"             => "Enter exchange rate to your default currency",
        "t_error_xendit_payment_failed"                            => "We are unable to handle your payment, Please try again",
        "t_custom_google_fonts_html"                               => "Custom Google fonts links",
        "t_enter_links_from_google_fonts"                          => "Enter links from Google fonts for your fonts",
        "t_font_name"                                              => "Font name",
        "t_enter_font_name"                                        => "Enter font name",
        "t_make_a_review"                                          => "Make a review", // Version 1.3.0
        "t_pls_enter_billing_info_to_checkout"                     => "Please enter your billing info first to checkout",
        "t_error_stripe_payment_failed"                            => "We couldn't handle your payment, please try again",
        "t_cron_jobs"                                              => "Cron jobs",
        "t_task_scheduling"                                        => "Task scheduling",
        "t_colors"                                                 => "Colors",
        "t_primary_color"                                          => "Primary color",
        "t_secondary_color"                                        => "Secondary color",
        "t_light_bg_color"                                         => "Ligh background color",
        "t_dark_bg_color"                                          => "Dark background color",
        "t_light_footer_color"                                     => "Light footer color",
        "t_dark_footer_color"                                      => "Dark footer color",
        "t_light_header_color"                                     => "Light header color",
        "t_dark_header_color"                                      => "Dark header color",
        "t_enable_dark_mode"                                       => "Enable dark mode",
        "t_projects"                                               => "Projects",
        "t_projects_settings"                                      => "Projects settings",
        "t_projects_settings_subtitle"                             => "Update projects section settings",
        "t_enable_projects_section"                                => "Enable projects section",
        "t_enable_free_posting"                                    => "Enable free posting",
        "t_enable_premium_posting"                                 => "Enable premium posting",
        "t_commission_from_publisher"                              => "Commission from publisher",
        "t_commission_from_freelancer"                             => "Commission from freelancer",
        "t_subscriptions"                                          => "Subscriptions",
        "t_projects_subscriptions"                                 => "Projects Subscriptions",
        "t_enable"                                                 => "Enable",
        "t_days"                                                   => "Days",
        "t_weeks"                                                  => "Weeks",
        "t_months"                                                 => "Months",
        "t_total_delivery_days"                                    => "Total: :days day(s)",
        "t_extra_delivery_days"                                    => "Extra delivery days",
        "t_disable"                                                => "Disable",
        "t_subscription_plans"                                     => "Subscription plans",
        "t_projects_subscription_plans_subtitle"                   => "When you enable premium posting, these packages will be visible for the publisher, he can choose one or more to post a project",
        "t_edit_project_plan"                                      => "Edit projects plan",
        "t_edit_project_plan_subtitle"                             => "Make modifications on this plan",
        "t_badge_text_color"                                       => "Badge text color",
        "t_badge_bg_color"                                         => "Badge background color",
        "t_plan_title"                                             => "Plan title",
        "t_enter_plan_title"                                       => "Enter plan title",
        "t_plan_description"                                       => "Plan description",
        "t_enter_plan_description"                                 => "Enter plan description",
        "t_plan_price"                                             => "Plan price",
        "t_enter_plan_price"                                       => "Enter plan price",
        "t_plan_days"                                              => "Plan days",
        "t_enter_plan_days"                                        => "Enter plan days",
        "t_enable_this_plan"                                       => "Enable this plan",
        "t_typography"                                             => "Typography",
        "t_general"                                                => "General",
        "t_auto_approve_projects"                                  => "Auto approve projects",
        "t_cloudinary"                                             => "Cloudinary",
        "t_cloudinary_url"                                         => "Cloudinary url",
        "t_enter_cloudinary_url"                                   => "Enter Cloudinary url",
        "t_upload_preset_name"                                     => "Upload preset name",
        "t_enter_upload_preset_name"                               => "Enter upload preset name",
        "t_sliders"                                                => "Sliders",
        "t_upload_home_sliders"                                    => "Upload home sliders",
        "t_validator_hex_color"                                    => "Field must be a valid color code",
        "t_font_family_hint"                                       => "e.g: Roboto",
        "t_explore_gigs"                                           => "Explore gigs",
        "t_explore_projects"                                       => "Explore projects",
        "t_deposit"                                                => "Deposit",
        "t_flutterwave_payment_settings"                           => "Flutterwave payment gateway",
        "t_flutterwave"                                            => "Flutterwave",
        "t_flutterwave_payment_settings_subtitle"                  => "Flutterwave is supported in Nigeria, Ghana, Kenya, South Africa, Uganda, Tanzania, the United Kingdom, America, and Europe.",
        "t_payment_gateway_logo"                                   => "Payment gateway logo",
        "t_encryption_key"                                         => "Encryption key",
        "t_enter_encryption_key"                                   => "Enter encryption key",
        "t_enter_a_percentage_value"                               => "Enter a percentage value",
        "t_deposit_fee"                                            => "Deposit fee",
        "t_vnpay_payment_settings"                                 => "VNPay payment gateway settings",
        "t_vnpay_payment_settings_subtitle"                        => "VNPay a payment gateway to accept payments in Vietnam",
        "t_vnpay_tmn_id"                                           => "Terminal ID / Website code",
        "t_enter_vnpay_tmn_id"                                     => "Enter terminal ID / Website code",
        "t_vnpay"                                                  => "VNPay",
        "t_paymob_payment_settings"                                => "Paymob settings",
        "t_paymob"                                                 => "Paymob",
        "t_paymob_payment_settings_subtitle"                       => "Paymob runs millions of transactions for different business sizes across the Middle East and Africa",
        "t_api_key"                                                => "Api key",
        "t_enter_api_key"                                          => "Enter api key",
        "t_hmac_hash"                                              => "HMAC hash",
        "t_enter_hmac_hash"                                        => "Enter HMAC hash",
        "t_merchant_id"                                            => "Merchant id",
        "t_enter_merchant_id"                                      => "Enter merchant id",
        "t_iframe_id"                                              => "Iframe id",
        "t_enter_iframe_id"                                        => "Enter iframe id",
        "t_integration_id"                                         => "Integration id",
        "t_enter_integration_id"                                   => "Enter integration id",
        "t_mercadopago"                                            => "Mercadopago",
        "t_mercadopago_payment_settings"                           => "Mercadopago settings",
        "t_mercadopago_payment_settings_subtitle"                  => "Mercadopago operations in Argentina, Brazil, Chile, Colombia, Mexico and Venezuela",
        "t_access_token"                                           => "Access token",
        "t_enter_access_token"                                     => "Enter access token",
        "t_paytabs"                                                => "Paytabs",
        "t_paytabs_payment_settings"                               => "Paytabs settings",
        "t_paytabs_payment_settings_subtitle"                      => "PayTabs is available in 17 countries (India, Indonesia, Malaysia, Philippines, Singapore, Hong Kong, Algeria, Bahrain, Egypt, Jordan, Kuwait, Lebanon, Morocco, Oman, Saudi-Arabia, Tunisia, UAE).",
        "t_region"                                                 => "Region",
        "t_choose_region"                                          => "Choose region",
        "t_profile_id"                                             => "Profile id",
        "t_enter_profile_id"                                       => "Enter profile id",
        "t_server_key"                                             => "Server key",
        "t_enter_server_key"                                       => "Enter server key",
        "t_razorpay_payment_settings"                              => "Razorpay settings",
        "t_razorpay_payment_settings_subtitle"                     => "Razorpay is only supported in India. Only the Indian Edition users of Zoho Inventory be able to integrate with Razorpay.",
        "t_key_id"                                                 => "Key id",
        "t_enter_key_id"                                           => "Enter key id",
        "t_key_secret"                                             => "Key secret",
        "t_enter_key_secret"                                       => "Enter key secret",
        "t_razorpay"                                               => "Razorpay",
        "t_mollie_payment_settings"                                => "Mollie settings",
        "t_mollie_payment_settings_subtitle"                       => "Mollie offers their services to companies based within the European Economic Area (EEA), Switzerland or the United Kingdom",
        "t_mollie"                                                 => "Mollie",
        "t_are_u_sure_unblock_conversation_user"                   => "Are you sure you want to unblock this user",
        "t_unblock_user"                                           => "Unblock user",
        "t_gig_created_subtitle_pending_approval"                  => "Your gig has been created and our team is reviewing it right now",
        "t_paytr_payment_settings"                                 => "PayTR settings",
        "t_paytr_payment_settings_subtitle"                        => "With PayTR Payment Gateway, you can accept payments in Turkey in five currencies.",
        "t_merchant_key"                                           => "Merchant key",
        "t_enter_merchant_key"                                     => "Enter merchant key",
        "t_merchant_salt"                                          => "Merchant salt",
        "t_enter_merchant_salt"                                    => "Enter merchant salt",
        "t_paytr"                                                  => "PayTR",
        "t_add_funds"                                              => "Add funds",
        "t_add_funds_subtitle"                                     => "Please choose how you would like to deposit",
        "t_fee"                                                    => "Fee",
        "t_u_will_get"                                             => "You will get",
        "t_transactions_history"                                   => "Transactions history",
        "t_next"                                                   => "Next",
        "t_deposit_amount_incorrect"                               => "The entered amount is invalid",
        "t_deposit_successful"                                     => "Deposit successful",
        "t_deposit_success_subtitle"                               => "Your request has been completed. You can track its progress on the transaction history page.",
        "t_view_history"                                           => "View history",
        "t_we_could_not_handle_ur_deposit_payment"                 => "We couldn't process your payment, please try again.",
        "t_ur_transaction_has_completed"                           => "Your transaction has completed successfully",
        "t_pay"                                                    => "Pay",
        "t_deposit_history"                                        => "Deposit history",
        "t_deposit_history_subtitle"                               => "Your latest deposit transactions",
        "t_deposit_offline_pending_msg"                            => "Your transaction has completed, but payment is still pending",
        "t_holder_name"                                            => "Holder name",
        "t_card_expiry_month"                                      => "Expiry month",
        "t_card_expiry_year"                                       => "Expiry year",
        "t_card_cvv"                                               => "CVV",
        "t_cashfre_error_authentication_error"                     => "Your x-client-id or x-client-secret are incorrect.",
        "t_cashfre_error_invalid_request_error"                    => "Your request body or request headers are not in order.",
        "t_cashfre_error_rate_limit_error"                         => "You breach our API limits",
        "t_cashfre_error_api_error"                                => "Something went wrong with the API. Please retry after sometime.",
        "t_pls_check_ur_inputs_and_try_again"                      => "Oops! Something is wrong, please verify your entries and try again",
        "t_google"                                                 => "Google",
        "t_mollie_received_amount_invalid"                         => "Invalid amount received. Please try again",
        "t_mollie_payment_pending"                                 => "Your deposit has been added, but not yet confirmed, We will update it automatically soon.",
        "t_mollie_payment_rejected"                                => "Payment failed. Please contact us for more information",
        "t_card_cvn"                                               => "CVN",
        "t_amount_must_be_integer"                                 => "Entered amount must be a valid integer number",
        "t_jazzcash"                                               => "JazzCash",
        "t_jazzcash_payment_settings"                              => "JazzCash settings",
        "t_jazzcash_payment_settings_subtitle"                     => "JazzCash is Pakistan largest mobile wallet trusted by millions of Pakistanis",
        "t_integerity_salt"                                        => "Integerity salt",
        "t_enter_integerity_salt"                                  => "Enter integerity salt",
        "t_sliders_settings_subtitle"                              => "Manage homepage sliders",
        "t_slider_uploaded_success"                                => "Slider has been successfully uploaded",
        "t_upload_new_slider"                                      => "Upload new slider",
        "t_slider_deleted_success"                                 => "Slider has been successfully deleted",
        "t_explore"                                                => "Explore",
        "t_explore_gigs_menu_subtitle"                             => "Find the perfect freelance services for your business",
        "t_popular_colon"                                          => "Popular:",
        "t_find_best"                                              => "Find best",
        "t_freelance"                                              => "Freelance",
        "t_services_for_ur_business"                               => "services for your business",
        "t_join"                                                   => "Join",
        "t_browse_categories"                                      => "Browse categories",
        "t_switch_to_selling"                                      => "Switch to selling",
        "t_switch_to_buying"                                       => "Switch to buying",
        "t_notifications"                                          => "Notifications",
        "t_cart"                                                   => "Cart",
        "t_dark_mode"                                              => "Dark mode",
        "t_light_mode"                                             => "Light mode",
        "t_enable_home_sliders"                                    => "Enable home sliders",
        "t_logo_height_hint"                                       => "pixel value, only numeric allowed",
        "t_logo_height"                                            => "Logo height",
        "t_enter_logo_height"                                      => "Enter logo height",
        "t_select_payout_method"                                   => "Select payout method",
        "t_wallet"                                                 => "Wallet",
        "t_generate_commands"                                      => "Generate commands",
        "t_regenerate_commands"                                    => "Regenerate commands",
        "t_amount_in_cart_not_equals_received"                     => "Amount received does not equal amount in your cart",
        "t_we_could_not_handle_ur_payment"                         => "We could not handle your payment, please try again.",
        "t_checkout_currency_invalid"                              => "Invalid checkout currency, please try again.",
        "t_site_logo_dark_mode"                                    => "Logo for dark mode",
        "t_how_it_works"                                           => "How it works",
        "t_create_a_gig"                                           => "Create a Gig",
        "t_start_selling_t_create_a_gig_subtitle"                  => "Sign up for free, set up your Gig, and offer your work to our global audience.",
        "t_deliver_great_work"                                     => "Deliver great work",
        "t_deliver_great_work_subtitle"                            => "Get notified when you get an order and use our system to discuss details with customers.",
        "t_get_paid"                                               => "Get paid",
        "t_get_paid_subtitle"                                      => "Get paid on time, every time. Payment is available for withdrawal as soon as it clears.",
        "t_work_ur_way"                                            => "Work your way",
        "t_u_bring_the_skill_make_earn_easy"                       => "You bring the skill. We will make earning easy.",
        "t_4_sec"                                                  => "4 sec",
        "t_a_gig_is_bought_every"                                  => "A Gig is bought every",
        "t_50_m_plus"                                              => "50M+",
        "t_transactions"                                           => "Transactions",
        "t_price_range"                                            => "Price range",
        "t_5_million"                                              => "5 Million",
        "t_active_monthly_visits"                                  => "Active monthly visits",
        "t_join_our_growing_freelance_community"                   => "Join our growing freelance community",
        "t_expert_freelancers"                                     => "Expert freelancers",
        "t_fake_name_irman_norton"                                 => "Irman Norton",
        "t_i_am_a_designer"                                        => "I am a designer",
        "t_fake_name_alejandro_lee"                                => "Alejandro Lee",
        "t_i_am_a_developer"                                       => "I am a developer",
        "t_fake_name_elsa_king"                                    => "Elsa King",
        "t_i_am_a_writer"                                          => "I am a writer",
        "t_fake_name_herman_reese"                                 => "Herman Reese",
        "t_i_am_a_video_editor"                                    => "I am a video editor",
        "t_fake_name_sue_keller"                                   => "Sue Keller",
        "t_i_am_a_musician"                                        => "I am a Musician",
        "t_buyer_stories"                                          => "Buyer stories",
        "t_buyer_story_1"                                          => "Riverr is an amazing resource for anyone in the startup space.",
        "t_buyer_story_2"                                          => "People love our logo, and we love Riverr.",
        "t_buyer_story_3"                                          => "There is no way I could have produced anything without Riverr.",
        "t_fake_name_alex_saunders"                                => "Alex Saunders",
        "t_fake_name_ricky_jones"                                  => "Ricky Jones",
        "t_fake_name_melissa_ross"                                 => "Melissa Ross",
        "t_entrepreneur"                                           => "Entrepreneur",
        "t_music_producer"                                         => "Music producer",
        "t_graphic_designer"                                       => "Graphic Designer",
        "t_what_can_i_sell_question"                               => "What can I sell?",
        "t_what_can_i_sell_answer"                                 => "Be creative! You can offer any service you wish as long as it's legal and complies with our terms. There are over 200 categories you can browse to get ideas.",
        "t_how_much_money_can_i_make_question"                     => "How much money can I make?",
        "t_how_much_money_can_i_make_answer"                       => "It's totally up to you. You can work as much as you want. Many sellers work on Riverr full time and some keep their 9-5 job while using Riverr to make extra money.",
        "t_how_much_does_it_cost_question"                         => "How much does it cost?",
        "t_how_much_does_it_cost_answer"                           => "It's free to join Riverr. There is no subscription required or fees to list your services. You keep 80% of each transaction.",
        "t_how_much_time_will_i_need_invest_question"              => "How much time will I need to invest?",
        "t_how_much_time_will_i_need_invest_answer"                => "It's very flexible. You need to put in some time and effort in the beginning to learn the marketplace and then you can decide for yourself what amount of work you want to do.",
        "t_how_do_i_price_my_service_question"                     => "How do I price my service?",
        "t_how_do_i_price_my_service_answer"                       => "You can set your pricing anywhere from $5 - $995 and offer your service to buyers.",
        "t_how_do_i_get_paid_question"                             => "How do I get paid?",
        "t_how_do_i_get_paid_answer"                               => "Once you complete a buyer's order, the money is transferred to your account. No need to chase clients for payments.",
        "t_signup_and_create_ur_first_gig"                         => "Sign up and create your first Gig",
        "t_today"                                                  => "today",
        "t_lets_get_started"                                       => "Let’s get started", // Version 1.3.1
        "t_system"                                                 => "System",
        "t_maintenance"                                            => "Maintenance",
        "t_logs"                                                   => "Logs",
        "t_upgrade"                                                => "Upgrade",
        "t_cache"                                                  => "Cache",
        "t_maintenance_mode"                                       => "Maintenance mode",
        "t_ur_order_processed_once_catch_pyt"                      => "Your order will be processed once we receive the payment",
        "t_user_balance"                                           => "User balance",
        "t_post_project"                                           => "Post project",
        "t_post_new_project"                                       => "Post new project",
        "t_post_project_description_hint"                          => "Type a brief description about your project, your business, and include an overview of what you need done.",
        "t_projects_categories"                                    => "Projects categories",
        "t_new_category"                                           => "New category",
        "t_create_projects_category"                               => "Create a projects category",
        "t_translations"                                           => "Translations",
        "t_projects_catgeories_no_translation_yet"                 => "You can create this category in other languages too",
        "t_add_new_translation"                                    => "Add new translation",
        "t_project_cat_trans_lang_key_exists_error"                => "Looks like you already added translation in this language",
        "t_edit_projects_category"                                 => "Edit projects category",
        "t_choose_category"                                        => "Choose category",
        "t_delete_project_cat_has_projects_hint"                   => "It looks like this category already has projects, Please select an alternative one to move them before you delete it.",
        "t_pls_select_altr_project_category"                       => "Please select an alternative category",
        "t_selected_altern_project_cat_not_found"                  => "Selected alternative category does not exist. Please try again",
        "t_delete_project_cat_confirm_msg"                         => "This action cannot be undone. Are you sure you want to delete this category?",
        "t_fixed_price"                                            => "Fixed price",
        "t_hourly_price"                                           => "Hourly price",
        "t_post_new_project_subtitle"                              => "Get started by creating a new project.",
        "t_min"                                                    => "Min",
        "t_max"                                                    => "Max",
        "t_post_project_category_hint"                             => "Select Category which fit your project requirements",
        "t_post_project_skills_hint"                               => "Enter up to 5 skills that best describe your project. Freelancers will use these skills to find projects they are most interested and experienced in",
        "t_what_skills_are_required"                               => "What skills are required?",
        "t_how_do_u_want_to_pay"                                   => "How do you want to pay?",
        "t_budget"                                                 => "Budget",
        "t_select"                                                 => "Select",
        "t_no_results_found"                                       => "No results found",
        "t_max_5_skills_allowed"                                   => "Maximum of 5 skills allowed",
        "t_toast_select_at_least_5_skills"                         => "Please select at least 5 skills",
        "t_invalid_price_format"                                   => "Invalid price format. Please try again",
        "t_make_ur_project_premium_optional"                       => "Make your project premium (Optional)",
        "t_make_ur_project_premium"                                => "Make your project premium",
        "t_promotion"                                              => "Promotion",
        "t_toast_select_plan_to_post_project"                      => "Please select a plan to post your project",
        "t_who_can_post_projects"                                  => "Who can post projects",
        "t_choose_who_can_post_new_projects"                       => "Choose who can post new projects",
        "t_both"                                                   => "Both",
        "t_plans"                                                  => "Plans",
        "t_projects_plans"                                         => "Projects plans",
        "t_ur_project_created_and_pending_payment"                 => "Your project has been successfully created, Please choose a method for payment",
        "t_ur_project_created_and_pending_approval"                => "Your project is pending approval now.",
        "t_ur_project_created_success"                             => "Your project has been successfully published",
        "t_max_project_price_must_be_greater"                      => "Max price must be greater than min price",
        "t_promote_project"                                        => "Promote project",
        "t_promote_project_subtitle"                               => "Get best results by making your project premium",
        "t_ur_payment_has_succeeded"                               => "Your payment has been successfully processed",
        "t_bid_now"                                                => "Bid Now",
        "t_urgent_project"                                         => "Urgent project",
        "t_project_details"                                        => "Project details",
        "t_required_skills"                                        => "Required skills",
        "t_bid_on_this_project"                                    => "Bid on this project",
        "t_beware_of_scams"                                        => "Beware of scams",
        "t_beware_of_scams_details"                                => "If you are being asked to pay a security deposit, or if you are being asked to chat on Telegram, WhatsApp, or another messaging platform, it is likely a scam. Report these projects or contact Support for assistance.",
        "t_report_project"                                         => "Report project",
        "t_share_project"                                          => "Share project",
        "t_project_details_subtitle"                               => "Project description and required skills",
        "t_project_summary"                                        => "Project summary",
        "t_duration"                                               => "Duration",
        "t_day"                                                    => "Day",
        "t_bids"                                                   => "Bids",
        "t_avg_bid"                                                => "Avg bid",
        "t_budget_type"                                            => "Budget type",
        "t_pending_approval"                                       => "Pending approval",
        "t_completed"                                              => "Completed",
        "t_under_development"                                      => "Under development",
        "t_pending_final_review"                                   => "Pending final review",
        "t_incomplete"                                             => "Incomplete",
        "t_impressions"                                            => "Impressions",
        "t_client_info"                                            => "Client info",
        "t_the_client_account_appear_if_contact_u"                 => "The client's account will appear to you if he contacts you",
        "t_project_summary_subtitle"                               => "Brief details about this project",
        "t_proposals"                                              => "Proposals",
        "t_proposals_subtitle"                                     => "Browse proposals from our freelancers",
        "t_edit_ur_bid"                                            => "Edit your bid",
        "t_verified_account"                                       => "Verified account",
        "t_report_bid"                                             => "Report bid",
        "t_validator_username"                                     => "username must contain only letters,numbers and underscores",
        "t_bid_details"                                            => "Bid details",
        "t_bid_amount"                                             => "Bid amount",
        "t_this_project_will_be_delivered_in"                      => "This project will be delivered in",
        "t_describe_ur_proposal"                                   => "Describe your proposal",
        "t_describe_ur_proposal_placeholder"                       => "enter a detailed description of your quotation that addresses the client’s requirements for the project",
        "t_continue"                                               => "Continue",
        "t_enable_promoting_bids"                                  => "Enable promoting bids",
        "t_plans_for_promoting_projects"                           => "Plans for promoting projects",
        "t_plans_for_promoting_bids"                               => "Plans for promoting proposals",
        "t_plans_for_promoting_bids_subtitle"                      => "These upgrades will be optional for the freelancer to promote his proposal",
        "t_sealed"                                                 => "Sealed",
        "t_sponsored"                                              => "Sponsored",
        "t_highlight"                                              => "Highlight",
        "t_bidding_plan_sponsored_subtitle"                        => "Bids that are sponsored are 518% more likely to be awarded. Stand out from the rest of the freelancers, by being pinned to the top of the employer’s bid list. There is only one sponsored bid per project.",
        "t_bidding_plan_sealed_subtitle"                           => "Upgrading to a sealed entry will hide your bid from other freelancers. This keeps others from copying your work, making your bid one-of-a-kind",
        "t_bidding_plan_highlight_subtitle"                        => "Make your bid highlighted in yellow for greater visibility to the employer and a higher chance of being awarded the project.",
        "t_optional_upgrades"                                      => "Optional upgrades",
        "t_paid_to_you"                                            => "Paid to you",
        "t_bid_paid_to_you_tooltip"                                => "This is the net amount paid to you",
        "t_dont_use_external_links"                                => "Don’t use external links",
        "t_by_add_proposal_u_agree_terms"                          => "By posting your proposal, you agree to be bound by Terms of service and privacy policy.",
        "t_pls_insert_bid_value_between_budget"                    => "Please insert a value between the budget",
        "t_error"                                                  => "Error",
        "t_project_not_active_to_send_proposals"                   => "This project is not active to send proposals",
        "t_u_cant_submit_bids_to_urself"                           => "You cannot bid on your projects.",
        "t_u_already_submitted_a_bid_to_this_project"              => "You already submitted your bid on this project",
        "t_auto_approve_bids"                                      => "Auto approve bids",
        "t_this_bid_sealed_explain"                                => "The sealed upgrade hides bids from other freelancers. This prevents others from copying bids, making them more unique.",
        "t_edit_bid"                                               => "Edit bid",
        "t_what_is_wrong_with_this_bid"                            => "What is wrong with this bid?",
        "t_report_bid_reason_1"                                    => "Bidder did not read project description",
        "t_report_bid_reason_2"                                    => "Unclear or does not provide enough information",
        "t_report_bid_reason_3"                                    => "Contains contact information",
        "t_report_bid_reason_4"                                    => "Other",
        "t_enter_issue_description"                                => "Enter issue description",
        "t_pls_login_or_register_report_bid"                       => "Please login or register to report this bid",
        "t_u_cannot_report_ur_own_bids"                            => "You cannot report your own bids",
        "t_u_already_reported_this_bid"                            => "You already reported this bid",
        "t_tnx_for_the_feedback"                                   => "Thanks for the feedback",
        "t_subject_admin_bid_reported"                             => "Someone has reported a bid",
        "t_notification_admin_reported_bid"                        => "A bid has been reported in this project",
        "t_reported_bids"                                          => "Reported bids",
        "t_newest"                                                 => "Newest",
        "t_oldest"                                                 => "Oldest",
        "t_fastest"                                                => "Fastest",
        "t_cheapest"                                               => "Cheapest",
        "t_copy"                                                   => "Copy",
        "t_report_project_reason_1"                                => "Contains contact information",
        "t_report_project_reason_2"                                => "Advertising another website",
        "t_report_project_reason_3"                                => "Fake project posted",
        "t_report_project_reason_4"                                => "Obscenities or harassing behaviour",
        "t_report_project_reason_5"                                => "Non-full time project posted requiring abnormal bidding",
        "t_report_project_reason_6"                                => "Other",
        "t_pls_login_or_register_report_project"                   => "Please login or register to report this project",
        "t_u_cannot_report_ur_own_projects"                        => "You cannot report your own projects",
        "t_u_already_reported_this_project"                        => "You already reported this project",
        "t_we_have_received_bid_report_success"                    => "We have received your report and will investigate and take action on it shortly.",
        "t_subject_admin_project_reported"                         => "Someone has reported a project",
        "t_notification_admin_reported_project"                    => "The following project has been reported",
        "t_reported_projects"                                      => "Reported projects",
        "t_this_project_is_closed_for_bidding"                     => "This project is closed for bidding",
        "t_find_another_project"                                   => "Find another project",
        "t_my_projects_subtitle"                                   => "Manage your projects",
        "t_confirm_delete"                                         => "Confirm delete",
        "t_are_u_sure_u_want_to_delete_this_project"               => "Are you sure you want to delete this project",
        "t_all_records_related_to_project_will_erased"             => "All records related to this project will be erased. This action cannot be undone.",
        "t_success"                                                => "Success",
        "t_open"                                                   => "Open",
        "t_milestones"                                             => "Milestones",
        "t_awarded"                                                => "Awarded",
        "t_award"                                                  => "Award",
        "t_revoke"                                                 => "Revoke",
        "t_chat_now"                                               => "Chat now",
        "t_u_dont_have_permissions_to_do_action"                   => "You don’t have permissions to do this action.",
        "t_this_bid_is_not_active_yet"                             => "This bid is not active yet. Please try again later.",
        "t_project_is_not_open_for_bid"                            => "This project is not open for bid right now",
        "t_this_bid_is_not_awarded_to_revoke_it"                   => "This bid is not awarded to revoke it",
        "t_u_have_success_revoked_this_bid"                        => "You have successfully revoked this offer",
        "t_u_have_accepted_this_bid_success"                       => "You have successfully accepted this offer",
        "t_info"                                                   => "Info",
        "t_welcome_back"                                           => "Welcome back",
        "t_pls_login_to_continue"                                  => "Please sign in to continue",
        "t_auth_screen_background_img"                             => "Authentication screen background image",
        "t_pls_signup_to_continue"                                 => "Please sign up to continue",
        "t_by_signup_u_agree_to_terms_privacy"                     => "By signing up, you agree to our privacy policy and terms of service.",
        "t_upload_default_image"                                   => "Upload default image",
        "t_hero_section"                                           => "Hero section",
        "t_hero_section_settings"                                  => "Hero section settings",
        "t_hero_section_settings_subtitle"                         => "Update the hero section settings",
        "t_enable_hero_section_background_img"                     => "Enable hero section background image",
        "t_large_screen_background_img"                            => "Large screen background image",
        "t_medium_screen_background_img"                           => "Medium screen background image",
        "t_small_screen_background_img"                            => "Small screen background image",
        "t_hero_section_default_bg_color"                          => "Hero section default background color",
        "t_large_screen_height"                                    => "Large screen height",
        "t_small_screen_height"                                    => "Small screen height",
        "t_enter_value_in_pixel"                                   => "Enter a pixel value",
        "t_youcanpay_payment_settings"                             => "YouCanPay settings",
        "t_gig_added_to_ur_cart"                                   => "Gig has been added to your cart",
        "t_deleted_users"                                          => "Deleted users",
        "t_move_to_trash"                                          => "Move to trash",
        "t_trashed_users"                                          => "Trashed users",
        "t_go_back"                                                => "Go back",
        "t_deleted_date"                                           => "Deleted date",
        "t_permanently_delete"                                     => "Permanently delete",
        "t_restore_user"                                           => "Restore user",
        "t_confirm_restore"                                        => "Confirm restore",
        "t_are_u_sure_u_want_to_restore_this_user"                 => "Are you sure you want to restore this user",
        "t_are_u_sure_u_want_to_restore_this_gig"                  => "Are you sure you want to restore this gig",
        "t_restore"                                                => "Restore",
        "t_are_u_sure_u_want_to_prmtly_delete_usr"                 => "Are you sure you want to permanently delete this user?",
        "t_are_u_sure_u_want_to_prmtly_delete_gig"                 => "Are you sure you want to permanently delete this gig?",
        "t_all_records_related_to_user_will_erased"                => "All records related to this user (gigs, transactions, conversations, reviews...) will be erased. This action cannot be undone.",
        "t_all_records_related_to_gig_will_erased"                 => "All records related to this gig (orders, transactions, refund, reviews...) will be erased. This action cannot be undone.",
        "t_deleted_gigs"                                           => "Deleted gigs",
        "t_trashed_gigs"                                           => "Trashed gigs",
        "t_restore_gig"                                            => "Restore gigs",
        "t_are_u_sure_u_want_to_delete_this_order"                 => "Are you sure you want to delete this order with all items? this action cannot be undone.",
        "t_delete_order"                                           => "Delete order",
        "t_rejection_reason"                                       => "Rejection reason",
        "t_enter_rejection_reason"                                 => "Enter rejection reason",
        "t_subject_everyone_recent_deposit_rejected"               => "Your recent deposit transaction has been rejected",
        "t_deposit_of_this_amount"                                 => "Deposit of this amount",
        "t_has_been_rejected_for_this_reason"                      => "Has been rejected for the following reason",
        "t_enter_credentials_to_access_dashboard"                  => "Enter credentials to access dashboard",
        "t_selected_gigs_for_u"                                    => "Selected gigs for you",
        "t_latest_projects"                                        => "Latest projects",
        "t_payouts"                                                => "Payouts",
        "t_selected_upgrades"                                      => "Selected upgrades",
        "t_notification_admin_bid_pending_approval"                => "Bid pending approval",
        "t_u_received_new_bid_on_ur_project"                       => "You have received a new bid for your project",
        "t_ur_bid_has_been_posted"                                 => "You bid on this project has been successfully posted",
        "t_ur_bid_has_been_posted_pending_approval"                => "Your bid on this project has been added and pending approval",
        "t_ur_bid_has_been_posted_pending_payment"                 => "Your bid has been posted and you will be redirect to payment form now",
        "t_promote_bid"                                            => "Promote bid",
        "t_projet_already_awarded_u_cant_promote_bid"              => "This project has already been awarded to someone else and you cannot promote your bid.",
        "t_promote_bid_subtitle"                                   => "Promote your bid to get higher chance to be awarded",
        "t_awarded_projects"                                       => "Awarded projects",
        "t_my_bids"                                                => "My bids",
        "t_accept_the_project"                                     => "Accept the project",
        "t_project_overview"                                       => "Project overview",
        "t_send_a_message"                                         => "Send a message",
        "t_awarded_projects_warning_msg"                           => "You can now start working on this project after the employer place a milestone payment. We strongly suggest that you do not start work until a Milestone Payment is in place. A Milestone Payment protects you because you can start work with peace of mind, knowing that the funds are there.
        ",
        "t_awarded_projects_subtitle"                              => "List of projects awarded to you from employers",
        "t_reject_the_project"                                     => "Reject the project",
        "t_pending_ur_approval"                                    => "Pending your approval",
        "t_u_have_36_hours_to_accept_project"                      => "Don’t keep them waiting! You have 36 hours to accept before the award of the project is retracted automatically.",
        "t_proposal_message"                                       => "Proposal message",
        "t_confirmation"                                           => "Confirmation",
        "t_accept_project_confirmation_freelancer_msg"             => "You will be redirect to the project overview section, where you can share files of the projects with the employer and request milestones.",
        "t_pls_let_us_know_why_reject_this_project_freelancer"     => "Please let us know why you want to reject this project.",
        "t_freelancer_reject_project_reason_1"                     => "Project is spam or fraud",
        "t_freelancer_reject_project_reason_2"                     => "The employer is unclear about what they want",
        "t_freelancer_reject_project_reason_3"                     => "The employer did not create a milestone",
        "t_freelancer_reject_project_reason_4"                     => "We do not agree on the budget",
        "t_freelancer_reject_project_reason_5"                     => "I already have enough work",
        "t_freelancer_reject_project_reason_6"                     => "I do not have the skills to do the project",
        "t_freelancer_reject_project_reason_7"                     => "I do not have time to take on the project",
        "t_freelancer_reject_project_reason_8"                     => "Other",
        "t_pls_select_rejection_reason"                            => "Please select a rejection reason first",
        "t_ur_bid_will_hidden_after_reject"                        => "Your bid on this project will be hidden after you reject this project",
        "t_freelancer_u_have_reject_this_project_success"          => "You have successfully rejected this project",
        "t_u_have_no_awarded_projects_yet"                         => "You have no awarded projects yet.",
        "t_freelancer_u_have_accepted_this_project_success"        => "You have successfully accepted this project",
        "t_no_project_found_in_our_records"                        => "No project found in our records. Please try again.",
        "t_caching"                                                => "Caching",
        "t_employer"                                               => "Employer",
        "t_view_project"                                           => "View project",
        "t_reject"                                                 => "Reject",
        "t_approve_project"                                        => "Approve project",
        "t_are_u_sure_approve_project_admin"                       => "Are you sure you want to approve the following project?",
        "t_reject_project"                                         => "Reject project",
        "t_delete_project_admin_alert"                             => "Please remember that anything related to this project will be erased, including bids, subscriptions, milestones... \n Make sure to delete only new projects, that has nothing yet. \n This action cannot be undone.",
        "t_edit_bidding_plan"                                      => "Edit bidding plan",
        "t_delete_payment"                                         => "Delete payment",
        "t_are_u_sure_u_want_approve_payment"                      => "Are you sure you want to approve this payment?",
        "t_are_u_sure_u_want_reject_payment"                       => "Are you sure you want to reject this payment?",
        "t_are_u_sure_delete_payment"                              => "Are you sure you want to delete this payment?",
        "t_freelancer"                                             => "Freelancer",
        "t_delete_bid"                                             => "Delete bid",
        "t_preview_bid"                                            => "Preview bid",
        "t_approve_bid"                                            => "Approve bid",
        "t_are_u_sure_approve_bid_admin"                           => "Are you sure you want to approve this bid?",
        "t_reject_bid"                                             => "Reject bid",
        "t_delete_bid_admin_alert"                                 => "Are you sure you want to delete this bid? Please make sure this bid is not awarded first.",
        "t_highlighted"                                            => "Highlighted",
        "t_bids_subscriptions"                                     => "Bids subscriptions",
        "t_create_skills"                                          => "Create skills",
        "t_skill"                                                  => "Skill",
        "t_delete_project_skill_admin_alert"                       => "Are you sure you want to delete this skill?",
        "t_enter_skill_name"                                       => "Enter skill name",
        "t_shared_files"                                           => "Shared files",
        "t_create_milestone"                                       => "Create milestone",
        "t_create_milestone_payment"                               => "Create milestone payment",
        "t_employer_u_dont_have_milestone_amount"                  => "You don’t have this amount in your wallet. Please make a deposit first.",
        "t_milestone_created_success"                              => "Your milestone payment was created successfully",
        "t_funded"                                                 => "Funded",
        "t_requested"                                              => "Requested",
        "t_release_payment"                                        => "Release payment",
        "t_confirm_release_of_pymnt_for"                           => "Confirm release of payment for",
        "t_pls_ensure_u_are_satisfied_with_work_release_milestone" => "Please ensure that you are satisfied with the work the freelancer has submitted, as Milestone Payments cannot be returned once released.",
        "t_milestone_payment_released_success"                     => "Milestone payment has been successfully released.",
        "t_u_cannot_create_milestones_for_this_project"            => "You cannot create milestone payment for this project",
        "t_crypto_currency"                                        => "Crypto currency",
        "t_enter_crypto_currency"                                  => "Enter crypto currency",
        "t_nowpayments_scan_qr_or_copy_pay_address_info"           => "Scan the QR code or copy the address above to send the payment, after that click on confirm button. Please do not refresh the page until you finish the payment.",
        "t_i_sent_the_money"                                       => "I sent the money",
        "t_ur_order_details_are_current_processed"                 => "Your order details are currently being processed.",                                                                                                                                                                     // Version 1.3.2
        "t_total_reach"                                            => "Total reach",
        "t_completed_orders"                                       => "Completed orders",
        "t_pending_orders"                                         => "Pending orders",
        "t_orders_under_progress"                                  => "Order under progress",
        "t_canceled_orders"                                        => "Canceled orders",
        "t_create_a_new_gig"                                       => "Create a new gig",
        "t_search_in_ur_contacts"                                  => "Search in your contacts",
        "t_you"                                                    => "You",
        "t_saved_messages"                                         => "Saved messages",
        "t_save_messages_secretly"                                 => "Save messages secretly",
        "t_emojis_flags"                                           => "Flags",
        "t_emojis_custom"                                          => "Custom",
        "t_emojis_activity"                                        => "Activity",
        "t_emojis_food_drink"                                      => "Food and drink",
        "t_emojis_symbols"                                         => "Symbols",
        "t_emojis_recently_used"                                   => "Recently used",
        "t_oops"                                                   => "Oops!",
        "t_emojis_animals_nature"                                  => "Animals and nature",
        "t_emojis_objects"                                         => "Objects",
        "t_emojis_travel"                                          => "Travel and Places",
        "t_emojis_smileys"                                         => "Smileys and People",
        "t_search_results"                                         => "Search results",
        "t_pick_an_emoji"                                          => "Pick an emoji",
        "t_choose_default_skin_tone"                               => "Choose default skin tone",
        "t_skin_default"                                           => "Default",
        "t_skin_light"                                             => "Light",
        "t_skin_medium_light"                                      => "Medium-Light",
        "t_skin_medium"                                            => "Medium",
        "t_skin_medium_dark"                                       => "Medium-Dark",
        "t_skin_dark"                                              => "Dark",
        "t_insert_emoji"                                           => "Insert emoji",
        "t_attach_a_file"                                          => "Attach a file",
        "t_send_audio_message"                                     => "Send audio message",
        "t_stop_recording"                                         => "Stop recording",
        "t_favorites"                                              => "Favorites",
        "t_type_to_search_in_ur_contacts"                          => "Type to search in your contacts",
        "t_chat_connected"                                         => "Connected",
        "t_chat_connecting"                                        => "Connecting...",
        "t_chat_no_internet_access"                                => "No internet access",
        "t_delete_conversation"                                    => "Delete conversation",
        "t_shared_photos"                                          => "Shared photos",
        "t_this_action_cannot_be_undone"                           => "This action cannot be undone.",
        "t_image"                                                  => "Image",
        "t_sorry_file_chat_does_not_exist"                         => "Sorry, File does not exist in our server or may have been deleted!",
        "t_nothing_shared_yet"                                     => "Nothing shared yet",
        "t_user_not_found"                                         => "User not found",
        "t_type_something_to_start_messaging"                      => "Type something to start messaging",
        "t_ur_contact_list_is_empty"                               => "Your contact list is empty",
        "t_backend_time_locale"                                    => "Backend time locale",
        "t_frontend_time_locale"                                   => "Frontend time locale",
        "t_choose_default_locale"                                  => "Choose default locale",
        "t_number_order_in_queue"                                  => ":number order in queue",
        "t_live_chat_settings"                                     => "Live chat settings",
        "t_live_chat_settings_sidebar"                             => "Live chat",
        "t_new_messages"                                           => "New messages",
        "t_latest_orders"                                          => "Latest orders",
        "t_latest_awarded_projects"                                => "Latest awarded projects",
        "t_back_to_my_works"                                       => "Back to my works",
        "t_my_dashboard"                                           => "My dashboard",
        "t_add_new_work"                                           => "Add a new work",
        "my_portfolio"                                             => "My portfolio",
        "t_edit_my_work"                                           => "Edit my work",
        "t_confirm_cancellation"                                   => "Confirm cancellation",
        "t_confirm"                                                => "Confirm",
        "t_payouts_history"                                        => "Payouts history",
        "t_request_payment"                                        => "Request a payment",
        "t_withdraw_funds"                                         => "Withdraw funds",
        "t_pls_review_ur_withdrawal_details"                       => "Please review your withdrawal details.",
        "t_requested_amount"                                       => "Requested amount",
        "t_withdrawal_fee"                                         => "Service fee",
        "t_submitted_proposals"                                    => "Submitted proposals",
        "t_my_proposal"                                            => "My proposal",
        "t_share_a_file"                                           => "Share a file",
        "t_cancel_project"                                         => "Cancel project",
        "t_milestone_payment_created"                              => "Milestone payment created",
        "t_milestone_payments"                                     => "Milestone payments",
        "t_request_milestone"                                      => "Request milestone",
        "t_no_milestone_payments_created_yet"                      => "No milestone payments created yet",
        "t_enter_milestone_payment_description"                    => "Enter milestone payment description",
        "t_project_budget"                                         => "Project budget",
        "t_paid_amount"                                            => "Paid amount",
        "t_u_exceeded_amount_agreed_with_employer_milestone"       => "You exceeded the project amount you agreed with the employer. Please try different amount.",
        "t_confirm_milestone_payment"                              => "Confirm milestone payment",
        "t_pls_review_ur_milestone_payment_details"                => "Please review your milestone payment details.",
        "t_milestone_freelancer_fee_name"                          => "service fee",
        "t_read_more"                                              => "Read more",
        "t_requires_changes"                                       => "Requires changes",
        "t_are_u_sure_u_want_to_delete_this_proposal"              => "Are you sure you want to delete this proposal?",
        "t_promote_ur_proposal"                                    => "Promote your propsal",
        "t_back_to_proposals"                                      => "Back to proposals",
        "t_ur_projects_page_is_empty"                              => "Your projects page is empty",
        "t_ur_projects_page_is_empty_subtitle"                     => "Explore latest projects and submit your proposals. Once you are awarded a project then you can see that flashing here.",
        "t_edit_my_proposal"                                       => "Edit my proposal",
        "t_more"                                                   => "More",
        "t_less"                                                   => "Less",
        "t_looks_like_u_already_awarded_project_other"             => "Looks like another freelancer already accepted your project.",
        "t_congratulations_employer_awarded_u_their_project_title" => "Congratulations! :username awarded you their project :title",
        "t_awarded_date"                                           => "Awarded date",
        "t_thumbnail"                                              => "Thumbnail",
        "t_browse_by_categories"                                   => "Browse by categories",
        "t_clear_scope"                                            => "Clear scope.",
        "t_upfront_price"                                          => "Upfront price.",
        "t_no_surprises"                                           => "No surprises.",
        "t_complete_ur_most_pressing_work_with_project_catatlog"   => "Complete your most pressing work with Project Catalog. Browse and submit your proposal to predefined projects in just a few clicks.",
        "t_type_something_to_search_in_projects"                   => "Type something to search in projects",
        "t_enable_theme_switcher"                                  => "Enable theme switcher",
        "t_default_theme"                                          => "Default theme",
        "t_light"                                                  => "Light",
        "t_dark"                                                   => "Dark",
        "t_refresh"                                                => "Refresh",
        "t_paid_to_freelancer"                                     => "Paid to freelancer",
        "t_release"                                                => "Release",
        "t_employer_does_not_have_money_to_fund_milestone"         => "Employer does not have this amount to fund it.",
        "t_milestone_employer_fee_name"                            => "Service fee",
        "t_confirm_deposit"                                        => "Confirm deposit",
        "t_confirm_deposit_milestone_subtitle_admin"               => "Are you sure you want to deposit this amount.",
        "t_amount_has_been_deposit_milestone_success"              => "Amount has been successfully deposited.",
        "t_confirm_release_milestone_payment"                      => "Are you sure you want to release this milestone payment? This action cannot be undone.",
        "t_confirm_release_of_amount"                              => "Confirm release of amount",
        "t_amount_has_been_released_milestone_success"             => "Milestone payment has been successfully released.",
        "t_congts_freelancer_ur_project_completed"                 => "Congratulations, your project has been successfully completed.",
        "t_proposal_amount"                                        => "Proposal amount",
        "t_back_to_bids"                                           => "Back to bids",
        "t_pending_deposit_transactions"                           => "Pending deposit transactions",
        "t_pending_projects"                                       => "Pending projects",
        "t_pending_bids_subscriptions"                             => "Pending bids subscriptions",
        "t_pending_projects_subscriptions"                         => "Pending projects subscriptions",
        "t_pending_support_messages"                               => "Pending support messages",
        "t_pending_checkout_payments"                              => "Pending checkout payments",
        "t_pending_bids"                                           => "Pending bids",
        "t_no_projects_yet"                                        => "No projects yet",
        "t_contact_skilled_freelancers_within_mintes"              => "Contact skilled freelancers within minutes. View profiles, ratings, portfolios and chat with them. Pay the freelancer only when you are 100% satisfied with their work.",
        "t_total_bids"                                             => "Total bids",
        "t_bid"                                                    => "Bid",
        "t_click"                                                  => "Click",
        "t_impression"                                             => "Impression",
        "t_needs_changes"                                          => "Needs changes",
        "t_contact_freelancer"                                     => "Contact freelancer",
        "t_finish_payment"                                         => "Finish payment",
        "t_submitted_date"                                         => "Submitted date",
        "t_employer_milestone_payments_subtitle"                   => "Show you are serious by depositing one or more milestone payments so the freelancer can start work. Milestone payments allow you to securely pay your freelancer.",
        "t_awarded_bid"                                            => "Awarded bid",
        "t_insufficient_funds_in_your_account"                     => "Insufficient funds in your account",
        "t_the_amount_to_be_paid_to_freelancer"                    => "The amount to be paid to the freelancer",
        "t_deposit_funds"                                          => "Deposit funds",
        "t_u_will_be_depositing_this_amount_employer_milestone"    => "You will be depositing this amount. When the milestone work is ready and you release the funds, we will send the funds to your freelancer.",
        "t_username_has_deposited_amount_in_project"               => ":username has deposited an :amount into your project",
        "t_confirm_release_of_payment_for_username"                => "confirm release of payment for :username",
        "t_pls_ensure_that_u_are_satisfied_with_work_freelancer"   => "Please ensure that you are satisfied with the work :username has submitted, as Milestone Payments cannot be returned once released.",
        "t_username_has_released_amount_in_project"                => ":username has released an amount of :amount into your account.",
        "t_milestone_payment_details"                              => "Milestone payment details",
        "t_project_completed"                                      => "Project completed",
        "t_subject_employer_freelancer_accepted_ur_project"        => "Your project has been accepted by the freelancer",
        "t_notification_username_has_accepted_ur_project"          => ":username has accepted to work on your project",
        "t_subject_employer_freelancer_rejected_ur_project"        => "Your project has been rejected by the freelancer",
        "t_notification_username_has_rejected_ur_project"          => ":username has rejected to work on your project",
        "t_subject_employer_freelancer_requested_a_milestone"      => "Freelancer has requested a milestone payment",
        "t_notification_username_has_requested_a_milestone"        => "A freelancer has requested a milestone payment",
        "t_subject_employer_project_completed"                     => "Project has been completed",
        "t_notification_ur_project_has_been_completed"             => "Your project has been completed by the freelancer",
        "t_subject_employer_project_approved"                      => "Project has been approved",
        "t_notification_ur_project_has_been_approved"              => "Your project has been approved",
        "t_subject_employer_project_needs_changes"                 => "Project needs changes",
        "t_notification_ur_project_has_been_rejected"              => "Your project needs some changes",
        "t_subject_everyone_u_received_new_bid"                    => "New bid has been received",
        "t_notification_u_have_received_a_new_bid"                 => "You have received a new bid",
        "t_subject_everyone_ur_bid_approved"                       => "Your bid has been approved",
        "t_notification_ur_bid_has_been_approved"                  => "Your bid has been approved successfully",
        "t_subject_everyone_ur_bid_needs_changes"                  => "You bid needs changes",
        "t_notification_ur_bid_has_been_rejected"                  => "Your recent proposal has been rejected and needs some changes",
        "t_subject_freelancer_employer_deposited_funds"            => "Employer has deposited funds",
        "t_notification_freelancer_employer_has_deposited_funds"   => "Employer has deposited funds in your project",
        "t_subject_freelancer_employer_released_funds"             => "Employer has released a payment",
        "t_notification_freelancer_employer_has_released_funds"    => "Employer has released funds in your porject",
        "t_subject_freelancer_u_awarded_a_project"                 => "You have awarded a project",
        "t_notification_congts_u_awarded_a_project"                => "Congratulations! you have awarded a project"
    ];