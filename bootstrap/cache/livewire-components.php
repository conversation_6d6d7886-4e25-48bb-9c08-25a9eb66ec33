<?php return array (
  'admin.advertisements.advertisements-component' => 'App\\Http\\Livewire\\Admin\\Advertisements\\AdvertisementsComponent',
  'admin.auth.login-component' => 'App\\Http\\Livewire\\Admin\\Auth\\LoginComponent',
  'admin.auth.logout-component' => 'App\\Http\\Livewire\\Admin\\Auth\\LogoutComponent',
  'admin.blog.articles-component' => 'App\\Http\\Livewire\\Admin\\Blog\\ArticlesComponent',
  'admin.blog.comments.comments-component' => 'App\\Http\\Livewire\\Admin\\Blog\\Comments\\CommentsComponent',
  'admin.blog.comments.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Blog\\Comments\\Options\\EditComponent',
  'admin.blog.options.create-component' => 'App\\Http\\Livewire\\Admin\\Blog\\Options\\CreateComponent',
  'admin.blog.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Blog\\Options\\EditComponent',
  'admin.blog.settings-component' => 'App\\Http\\Livewire\\Admin\\Blog\\SettingsComponent',
  'admin.categories.categories-component' => 'App\\Http\\Livewire\\Admin\\Categories\\CategoriesComponent',
  'admin.categories.options.create-component' => 'App\\Http\\Livewire\\Admin\\Categories\\Options\\CreateComponent',
  'admin.categories.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Categories\\Options\\EditComponent',
  'admin.conversations.conversations-component' => 'App\\Http\\Livewire\\Admin\\Conversations\\ConversationsComponent',
  'admin.conversations.messages-component' => 'App\\Http\\Livewire\\Admin\\Conversations\\MessagesComponent',
  'admin.countries.countries-component' => 'App\\Http\\Livewire\\Admin\\Countries\\CountriesComponent',
  'admin.countries.options.create-component' => 'App\\Http\\Livewire\\Admin\\Countries\\Options\\CreateComponent',
  'admin.countries.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Countries\\Options\\EditComponent',
  'admin.gigs.gigs-component' => 'App\\Http\\Livewire\\Admin\\Gigs\\GigsComponent',
  'admin.gigs.options.analytics-component' => 'App\\Http\\Livewire\\Admin\\Gigs\\Options\\AnalyticsComponent',
  'admin.gigs.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Gigs\\Options\\EditComponent',
  'admin.gigs.options.steps.gallery' => 'App\\Http\\Livewire\\Admin\\Gigs\\Options\\Steps\\Gallery',
  'admin.gigs.options.steps.overview' => 'App\\Http\\Livewire\\Admin\\Gigs\\Options\\Steps\\Overview',
  'admin.gigs.options.steps.pricing' => 'App\\Http\\Livewire\\Admin\\Gigs\\Options\\Steps\\Pricing',
  'admin.gigs.options.steps.requirements' => 'App\\Http\\Livewire\\Admin\\Gigs\\Options\\Steps\\Requirements',
  'admin.gigs.trash.trash-component' => 'App\\Http\\Livewire\\Admin\\Gigs\\Trash\\TrashComponent',
  'admin.home.home-component' => 'App\\Http\\Livewire\\Admin\\Home\\HomeComponent',
  'admin.includes.header' => 'App\\Http\\Livewire\\Admin\\Includes\\Header',
  'admin.includes.sidebar' => 'App\\Http\\Livewire\\Admin\\Includes\\Sidebar',
  'admin.invoices.invoices-component' => 'App\\Http\\Livewire\\Admin\\Invoices\\InvoicesComponent',
  'admin.languages.languages-component' => 'App\\Http\\Livewire\\Admin\\Languages\\LanguagesComponent',
  'admin.languages.options.create-component' => 'App\\Http\\Livewire\\Admin\\Languages\\Options\\CreateComponent',
  'admin.languages.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Languages\\Options\\EditComponent',
  'admin.languages.options.translate-component' => 'App\\Http\\Livewire\\Admin\\Languages\\Options\\TranslateComponent',
  'admin.levels.levels-component' => 'App\\Http\\Livewire\\Admin\\Levels\\LevelsComponent',
  'admin.levels.options.create-component' => 'App\\Http\\Livewire\\Admin\\Levels\\Options\\CreateComponent',
  'admin.levels.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Levels\\Options\\EditComponent',
  'admin.newsletter.newsletter-component' => 'App\\Http\\Livewire\\Admin\\Newsletter\\NewsletterComponent',
  'admin.newsletter.settings-component' => 'App\\Http\\Livewire\\Admin\\Newsletter\\SettingsComponent',
  'admin.orders.options.details-component' => 'App\\Http\\Livewire\\Admin\\Orders\\Options\\DetailsComponent',
  'admin.orders.orders-component' => 'App\\Http\\Livewire\\Admin\\Orders\\OrdersComponent',
  'admin.pages.options.create-component' => 'App\\Http\\Livewire\\Admin\\Pages\\Options\\CreateComponent',
  'admin.pages.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Pages\\Options\\EditComponent',
  'admin.pages.pages-component' => 'App\\Http\\Livewire\\Admin\\Pages\\PagesComponent',
  'admin.portfolios.portfolios-component' => 'App\\Http\\Livewire\\Admin\\Portfolios\\PortfoliosComponent',
  'admin.profile.profile-component' => 'App\\Http\\Livewire\\Admin\\Profile\\ProfileComponent',
  'admin.projects.bids.bids-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Bids\\BidsComponent',
  'admin.projects.bids.subscriptions.subscriptions-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Bids\\Subscriptions\\SubscriptionsComponent',
  'admin.projects.categories.categories-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Categories\\CategoriesComponent',
  'admin.projects.categories.options.create-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Categories\\Options\\CreateComponent',
  'admin.projects.categories.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Categories\\Options\\EditComponent',
  'admin.projects.milestones.milestones-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Milestones\\MilestonesComponent',
  'admin.projects.plans.bidding.edit-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Plans\\Bidding\\EditComponent',
  'admin.projects.plans.edit-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Plans\\EditComponent',
  'admin.projects.plans.plans-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Plans\\PlansComponent',
  'admin.projects.projects-component' => 'App\\Http\\Livewire\\Admin\\Projects\\ProjectsComponent',
  'admin.projects.settings-component' => 'App\\Http\\Livewire\\Admin\\Projects\\SettingsComponent',
  'admin.projects.skills.options.create-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Skills\\Options\\CreateComponent',
  'admin.projects.skills.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Skills\\Options\\EditComponent',
  'admin.projects.skills.skills-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Skills\\SkillsComponent',
  'admin.projects.subscriptions.subscriptions-component' => 'App\\Http\\Livewire\\Admin\\Projects\\Subscriptions\\SubscriptionsComponent',
  'admin.refunds.options.details-component' => 'App\\Http\\Livewire\\Admin\\Refunds\\Options\\DetailsComponent',
  'admin.refunds.refunds-component' => 'App\\Http\\Livewire\\Admin\\Refunds\\RefundsComponent',
  'admin.reports.bids-component' => 'App\\Http\\Livewire\\Admin\\Reports\\BidsComponent',
  'admin.reports.gigs-component' => 'App\\Http\\Livewire\\Admin\\Reports\\GigsComponent',
  'admin.reports.projects-component' => 'App\\Http\\Livewire\\Admin\\Reports\\ProjectsComponent',
  'admin.reports.users-component' => 'App\\Http\\Livewire\\Admin\\Reports\\UsersComponent',
  'admin.reviews.reviews-component' => 'App\\Http\\Livewire\\Admin\\Reviews\\ReviewsComponent',
  'admin.services.cashfree-component' => 'App\\Http\\Livewire\\Admin\\Services\\CashfreeComponent',
  'admin.services.cloudinary-component' => 'App\\Http\\Livewire\\Admin\\Services\\CloudinaryComponent',
  'admin.services.epoint-component' => 'App\\Http\\Livewire\\Admin\\Services\\EpointComponent',
  'admin.services.flutterwave-component' => 'App\\Http\\Livewire\\Admin\\Services\\FlutterwaveComponent',
  'admin.services.jazzcash-component' => 'App\\Http\\Livewire\\Admin\\Services\\JazzcashComponent',
  'admin.services.mercadopago-component' => 'App\\Http\\Livewire\\Admin\\Services\\MercadopagoComponent',
  'admin.services.mollie-component' => 'App\\Http\\Livewire\\Admin\\Services\\MollieComponent',
  'admin.services.nowpayments-component' => 'App\\Http\\Livewire\\Admin\\Services\\NowpaymentsComponent',
  'admin.services.offline-component' => 'App\\Http\\Livewire\\Admin\\Services\\OfflineComponent',
  'admin.services.paymob-component' => 'App\\Http\\Livewire\\Admin\\Services\\PaymobComponent',
  'admin.services.paypal-component' => 'App\\Http\\Livewire\\Admin\\Services\\PaypalComponent',
  'admin.services.paystack-component' => 'App\\Http\\Livewire\\Admin\\Services\\PaystackComponent',
  'admin.services.paytabs-component' => 'App\\Http\\Livewire\\Admin\\Services\\PaytabsComponent',
  'admin.services.paytr-component' => 'App\\Http\\Livewire\\Admin\\Services\\PaytrComponent',
  'admin.services.razorpay-component' => 'App\\Http\\Livewire\\Admin\\Services\\RazorpayComponent',
  'admin.services.recaptcha-component' => 'App\\Http\\Livewire\\Admin\\Services\\RecaptchaComponent',
  'admin.services.stripe-component' => 'App\\Http\\Livewire\\Admin\\Services\\StripeComponent',
  'admin.services.v-n-pay-component' => 'App\\Http\\Livewire\\Admin\\Services\\VNPayComponent',
  'admin.services.xendit-component' => 'App\\Http\\Livewire\\Admin\\Services\\XenditComponent',
  'admin.services.youcanpay-component' => 'App\\Http\\Livewire\\Admin\\Services\\YoucanpayComponent',
  'admin.settings.appearance-component' => 'App\\Http\\Livewire\\Admin\\Settings\\AppearanceComponent',
  'admin.settings.auth-component' => 'App\\Http\\Livewire\\Admin\\Settings\\AuthComponent',
  'admin.settings.chat-component' => 'App\\Http\\Livewire\\Admin\\Settings\\ChatComponent',
  'admin.settings.commission-component' => 'App\\Http\\Livewire\\Admin\\Settings\\CommissionComponent',
  'admin.settings.currency-component' => 'App\\Http\\Livewire\\Admin\\Settings\\CurrencyComponent',
  'admin.settings.footer-component' => 'App\\Http\\Livewire\\Admin\\Settings\\FooterComponent',
  'admin.settings.general-component' => 'App\\Http\\Livewire\\Admin\\Settings\\GeneralComponent',
  'admin.settings.hero-component' => 'App\\Http\\Livewire\\Admin\\Settings\\HeroComponent',
  'admin.settings.media-component' => 'App\\Http\\Livewire\\Admin\\Settings\\MediaComponent',
  'admin.settings.publish-component' => 'App\\Http\\Livewire\\Admin\\Settings\\PublishComponent',
  'admin.settings.security-component' => 'App\\Http\\Livewire\\Admin\\Settings\\SecurityComponent',
  'admin.settings.seo-component' => 'App\\Http\\Livewire\\Admin\\Settings\\SeoComponent',
  'admin.settings.smtp-component' => 'App\\Http\\Livewire\\Admin\\Settings\\SmtpComponent',
  'admin.settings.withdrawal-component' => 'App\\Http\\Livewire\\Admin\\Settings\\WithdrawalComponent',
  'admin.subcategories.options.create-component' => 'App\\Http\\Livewire\\Admin\\Subcategories\\Options\\CreateComponent',
  'admin.subcategories.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Subcategories\\Options\\EditComponent',
  'admin.subcategories.subcategories-component' => 'App\\Http\\Livewire\\Admin\\Subcategories\\SubcategoriesComponent',
  'admin.support.support-component' => 'App\\Http\\Livewire\\Admin\\Support\\SupportComponent',
  'admin.system.cache-component' => 'App\\Http\\Livewire\\Admin\\System\\CacheComponent',
  'admin.system.crontab-component' => 'App\\Http\\Livewire\\Admin\\System\\CrontabComponent',
  'admin.system.maintenance-component' => 'App\\Http\\Livewire\\Admin\\System\\MaintenanceComponent',
  'admin.users.options.balance-component' => 'App\\Http\\Livewire\\Admin\\Users\\Options\\BalanceComponent',
  'admin.users.options.create-component' => 'App\\Http\\Livewire\\Admin\\Users\\Options\\CreateComponent',
  'admin.users.options.details-component' => 'App\\Http\\Livewire\\Admin\\Users\\Options\\DetailsComponent',
  'admin.users.options.edit-component' => 'App\\Http\\Livewire\\Admin\\Users\\Options\\EditComponent',
  'admin.users.transactions.transactions-component' => 'App\\Http\\Livewire\\Admin\\Users\\Transactions\\TransactionsComponent',
  'admin.users.trash.trash-component' => 'App\\Http\\Livewire\\Admin\\Users\\Trash\\TrashComponent',
  'admin.users.users-component' => 'App\\Http\\Livewire\\Admin\\Users\\UsersComponent',
  'admin.verifications.verifications-component' => 'App\\Http\\Livewire\\Admin\\Verifications\\VerificationsComponent',
  'admin.withdrawals.withdrawals-component' => 'App\\Http\\Livewire\\Admin\\Withdrawals\\WithdrawalsComponent',
  'installation.administrator-component' => 'App\\Http\\Livewire\\Installation\\AdministratorComponent',
  'installation.crontab-component' => 'App\\Http\\Livewire\\Installation\\CrontabComponent',
  'installation.database-component' => 'App\\Http\\Livewire\\Installation\\DatabaseComponent',
  'installation.finish-component' => 'App\\Http\\Livewire\\Installation\\FinishComponent',
  'installation.requirements-component' => 'App\\Http\\Livewire\\Installation\\RequirementsComponent',
  'installation.start-component' => 'App\\Http\\Livewire\\Installation\\StartComponent',
  'main.account.billing.billing-component' => 'App\\Http\\Livewire\\Main\\Account\\Billing\\BillingComponent',
  'main.account.deposit.deposit-component' => 'App\\Http\\Livewire\\Main\\Account\\Deposit\\DepositComponent',
  'main.account.deposit.history-component' => 'App\\Http\\Livewire\\Main\\Account\\Deposit\\HistoryComponent',
  'main.account.favorite.favorite-component' => 'App\\Http\\Livewire\\Main\\Account\\Favorite\\FavoriteComponent',
  'main.account.orders.options.files-component' => 'App\\Http\\Livewire\\Main\\Account\\Orders\\Options\\FilesComponent',
  'main.account.orders.options.requirements-component' => 'App\\Http\\Livewire\\Main\\Account\\Orders\\Options\\RequirementsComponent',
  'main.account.orders.orders-component' => 'App\\Http\\Livewire\\Main\\Account\\Orders\\OrdersComponent',
  'main.account.password.password-component' => 'App\\Http\\Livewire\\Main\\Account\\Password\\PasswordComponent',
  'main.account.profile.profile-component' => 'App\\Http\\Livewire\\Main\\Account\\Profile\\ProfileComponent',
  'main.account.projects.options.checkout-component' => 'App\\Http\\Livewire\\Main\\Account\\Projects\\Options\\CheckoutComponent',
  'main.account.projects.options.milestones-component' => 'App\\Http\\Livewire\\Main\\Account\\Projects\\Options\\MilestonesComponent',
  'main.account.projects.projects-component' => 'App\\Http\\Livewire\\Main\\Account\\Projects\\ProjectsComponent',
  'main.account.refunds.options.details-component' => 'App\\Http\\Livewire\\Main\\Account\\Refunds\\Options\\DetailsComponent',
  'main.account.refunds.options.request-component' => 'App\\Http\\Livewire\\Main\\Account\\Refunds\\Options\\RequestComponent',
  'main.account.refunds.refunds-component' => 'App\\Http\\Livewire\\Main\\Account\\Refunds\\RefundsComponent',
  'main.account.reviews.options.create-component' => 'App\\Http\\Livewire\\Main\\Account\\Reviews\\Options\\CreateComponent',
  'main.account.reviews.options.edit-component' => 'App\\Http\\Livewire\\Main\\Account\\Reviews\\Options\\EditComponent',
  'main.account.reviews.options.preview-component' => 'App\\Http\\Livewire\\Main\\Account\\Reviews\\Options\\PreviewComponent',
  'main.account.reviews.reviews-component' => 'App\\Http\\Livewire\\Main\\Account\\Reviews\\ReviewsComponent',
  'main.account.settings.settings-component' => 'App\\Http\\Livewire\\Main\\Account\\Settings\\SettingsComponent',
  'main.account.verification.verification-component' => 'App\\Http\\Livewire\\Main\\Account\\Verification\\VerificationComponent',
  'main.auth.login-component' => 'App\\Http\\Livewire\\Main\\Auth\\LoginComponent',
  'main.auth.logout-component' => 'App\\Http\\Livewire\\Main\\Auth\\LogoutComponent',
  'main.auth.password.reset-component' => 'App\\Http\\Livewire\\Main\\Auth\\Password\\ResetComponent',
  'main.auth.password.update-component' => 'App\\Http\\Livewire\\Main\\Auth\\Password\\UpdateComponent',
  'main.auth.register-component' => 'App\\Http\\Livewire\\Main\\Auth\\RegisterComponent',
  'main.auth.request-component' => 'App\\Http\\Livewire\\Main\\Auth\\RequestComponent',
  'main.auth.social.casdoor.callback-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Casdoor\\CallbackComponent',
  'main.auth.social.casdoor.redirect-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Casdoor\\RedirectComponent',
  'main.auth.social.facebook.callback-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Facebook\\CallbackComponent',
  'main.auth.social.facebook.redirect-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Facebook\\RedirectComponent',
  'main.auth.social.github.callback-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Github\\CallbackComponent',
  'main.auth.social.github.redirect-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Github\\RedirectComponent',
  'main.auth.social.google.callback-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Google\\CallbackComponent',
  'main.auth.social.google.redirect-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Google\\RedirectComponent',
  'main.auth.social.linkedin.callback-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Linkedin\\CallbackComponent',
  'main.auth.social.linkedin.redirect-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Linkedin\\RedirectComponent',
  'main.auth.social.twitter.callback-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Twitter\\CallbackComponent',
  'main.auth.social.twitter.redirect-component' => 'App\\Http\\Livewire\\Main\\Auth\\Social\\Twitter\\RedirectComponent',
  'main.auth.verify-component' => 'App\\Http\\Livewire\\Main\\Auth\\VerifyComponent',
  'main.become.seller-component' => 'App\\Http\\Livewire\\Main\\Become\\SellerComponent',
  'main.blog.article-component' => 'App\\Http\\Livewire\\Main\\Blog\\ArticleComponent',
  'main.blog.blog-component' => 'App\\Http\\Livewire\\Main\\Blog\\BlogComponent',
  'main.cards.bid' => 'App\\Http\\Livewire\\Main\\Cards\\Bid',
  'main.cards.gig' => 'App\\Http\\Livewire\\Main\\Cards\\Gig',
  'main.cards.project' => 'App\\Http\\Livewire\\Main\\Cards\\Project',
  'main.cart.cart-component' => 'App\\Http\\Livewire\\Main\\Cart\\CartComponent',
  'main.categories.category-component' => 'App\\Http\\Livewire\\Main\\Categories\\CategoryComponent',
  'main.categories.subcategory-component' => 'App\\Http\\Livewire\\Main\\Categories\\SubcategoryComponent',
  'main.checkout.callback.epoint-component' => 'App\\Http\\Livewire\\Main\\Checkout\\Callback\\EpointComponent',
  'main.checkout.callback.flutterwave-component' => 'App\\Http\\Livewire\\Main\\Checkout\\Callback\\FlutterwaveComponent',
  'main.checkout.callback.mercadopago-component' => 'App\\Http\\Livewire\\Main\\Checkout\\Callback\\MercadopagoComponent',
  'main.checkout.callback.mollie-component' => 'App\\Http\\Livewire\\Main\\Checkout\\Callback\\MollieComponent',
  'main.checkout.callback.paytabs-component' => 'App\\Http\\Livewire\\Main\\Checkout\\Callback\\PaytabsComponent',
  'main.checkout.callback.stripe-component' => 'App\\Http\\Livewire\\Main\\Checkout\\Callback\\StripeComponent',
  'main.checkout.callback.vnpay-component' => 'App\\Http\\Livewire\\Main\\Checkout\\Callback\\VnpayComponent',
  'main.checkout.callback.xendit-component' => 'App\\Http\\Livewire\\Main\\Checkout\\Callback\\XenditComponent',
  'main.checkout.callback.youcanpay-component' => 'App\\Http\\Livewire\\Main\\Checkout\\Callback\\YoucanpayComponent',
  'main.checkout.checkout-component' => 'App\\Http\\Livewire\\Main\\Checkout\\CheckoutComponent',
  'main.create.create-component' => 'App\\Http\\Livewire\\Main\\Create\\CreateComponent',
  'main.create.steps' => 'App\\Http\\Livewire\\Main\\Create\\Steps',
  'main.create.steps.gallery' => 'App\\Http\\Livewire\\Main\\Create\\Steps\\Gallery',
  'main.create.steps.overview' => 'App\\Http\\Livewire\\Main\\Create\\Steps\\Overview',
  'main.create.steps.pricing' => 'App\\Http\\Livewire\\Main\\Create\\Steps\\Pricing',
  'main.create.steps.requirements' => 'App\\Http\\Livewire\\Main\\Create\\Steps\\Requirements',
  'main.explore.projects.category-component' => 'App\\Http\\Livewire\\Main\\Explore\\Projects\\CategoryComponent',
  'main.explore.projects.projects-component' => 'App\\Http\\Livewire\\Main\\Explore\\Projects\\ProjectsComponent',
  'main.explore.projects.skill-component' => 'App\\Http\\Livewire\\Main\\Explore\\Projects\\SkillComponent',
  'main.help.contact.contact-component' => 'App\\Http\\Livewire\\Main\\Help\\Contact\\ContactComponent',
  'main.hire.hire-component' => 'App\\Http\\Livewire\\Main\\Hire\\HireComponent',
  'main.home.home-component' => 'App\\Http\\Livewire\\Main\\Home\\HomeComponent',
  'main.includes.footer' => 'App\\Http\\Livewire\\Main\\Includes\\Footer',
  'main.includes.header' => 'App\\Http\\Livewire\\Main\\Includes\\Header',
  'main.messages.conversation-component' => 'App\\Http\\Livewire\\Main\\Messages\\ConversationComponent',
  'main.messages.messages-component' => 'App\\Http\\Livewire\\Main\\Messages\\MessagesComponent',
  'main.messages.new-component' => 'App\\Http\\Livewire\\Main\\Messages\\NewComponent',
  'main.newsletter.verify-component' => 'App\\Http\\Livewire\\Main\\Newsletter\\VerifyComponent',
  'main.page.page-component' => 'App\\Http\\Livewire\\Main\\Page\\PageComponent',
  'main.partials.cart' => 'App\\Http\\Livewire\\Main\\Partials\\Cart',
  'main.partials.languages' => 'App\\Http\\Livewire\\Main\\Partials\\Languages',
  'main.partials.search' => 'App\\Http\\Livewire\\Main\\Partials\\Search',
  'main.post.project.project-component' => 'App\\Http\\Livewire\\Main\\Post\\Project\\ProjectComponent',
  'main.profile.portfolio-component' => 'App\\Http\\Livewire\\Main\\Profile\\PortfolioComponent',
  'main.profile.profile-component' => 'App\\Http\\Livewire\\Main\\Profile\\ProfileComponent',
  'main.profile.project-component' => 'App\\Http\\Livewire\\Main\\Profile\\ProjectComponent',
  'main.project.project-component' => 'App\\Http\\Livewire\\Main\\Project\\ProjectComponent',
  'main.redirect.redirect-component' => 'App\\Http\\Livewire\\Main\\Redirect\\RedirectComponent',
  'main.reviews.reviews-component' => 'App\\Http\\Livewire\\Main\\Reviews\\ReviewsComponent',
  'main.search.search-component' => 'App\\Http\\Livewire\\Main\\Search\\SearchComponent',
  'main.seller.earnings.earnings-component' => 'App\\Http\\Livewire\\Main\\Seller\\Earnings\\EarningsComponent',
  'main.seller.gigs.gigs-component' => 'App\\Http\\Livewire\\Main\\Seller\\Gigs\\GigsComponent',
  'main.seller.gigs.options.analytics-component' => 'App\\Http\\Livewire\\Main\\Seller\\Gigs\\Options\\AnalyticsComponent',
  'main.seller.gigs.options.edit-component' => 'App\\Http\\Livewire\\Main\\Seller\\Gigs\\Options\\EditComponent',
  'main.seller.gigs.options.steps.gallery' => 'App\\Http\\Livewire\\Main\\Seller\\Gigs\\Options\\Steps\\Gallery',
  'main.seller.gigs.options.steps.overview' => 'App\\Http\\Livewire\\Main\\Seller\\Gigs\\Options\\Steps\\Overview',
  'main.seller.gigs.options.steps.pricing' => 'App\\Http\\Livewire\\Main\\Seller\\Gigs\\Options\\Steps\\Pricing',
  'main.seller.gigs.options.steps.requirements' => 'App\\Http\\Livewire\\Main\\Seller\\Gigs\\Options\\Steps\\Requirements',
  'main.seller.home.home-component' => 'App\\Http\\Livewire\\Main\\Seller\\Home\\HomeComponent',
  'main.seller.orders.options.deliver-component' => 'App\\Http\\Livewire\\Main\\Seller\\Orders\\Options\\DeliverComponent',
  'main.seller.orders.options.details-component' => 'App\\Http\\Livewire\\Main\\Seller\\Orders\\Options\\DetailsComponent',
  'main.seller.orders.options.requirements-component' => 'App\\Http\\Livewire\\Main\\Seller\\Orders\\Options\\RequirementsComponent',
  'main.seller.orders.orders-component' => 'App\\Http\\Livewire\\Main\\Seller\\Orders\\OrdersComponent',
  'main.seller.portfolio.options.create-component' => 'App\\Http\\Livewire\\Main\\Seller\\Portfolio\\Options\\CreateComponent',
  'main.seller.portfolio.options.edit-component' => 'App\\Http\\Livewire\\Main\\Seller\\Portfolio\\Options\\EditComponent',
  'main.seller.portfolio.portfolio-component' => 'App\\Http\\Livewire\\Main\\Seller\\Portfolio\\PortfolioComponent',
  'main.seller.projects.bids.bids-component' => 'App\\Http\\Livewire\\Main\\Seller\\Projects\\Bids\\BidsComponent',
  'main.seller.projects.bids.options.checkout-component' => 'App\\Http\\Livewire\\Main\\Seller\\Projects\\Bids\\Options\\CheckoutComponent',
  'main.seller.projects.bids.options.edit-component' => 'App\\Http\\Livewire\\Main\\Seller\\Projects\\Bids\\Options\\EditComponent',
  'main.seller.projects.milestones.milestones-component' => 'App\\Http\\Livewire\\Main\\Seller\\Projects\\Milestones\\MilestonesComponent',
  'main.seller.projects.projects-component' => 'App\\Http\\Livewire\\Main\\Seller\\Projects\\ProjectsComponent',
  'main.seller.refunds.options.details-component' => 'App\\Http\\Livewire\\Main\\Seller\\Refunds\\Options\\DetailsComponent',
  'main.seller.refunds.refunds-component' => 'App\\Http\\Livewire\\Main\\Seller\\Refunds\\RefundsComponent',
  'main.seller.reviews.options.details-component' => 'App\\Http\\Livewire\\Main\\Seller\\Reviews\\Options\\DetailsComponent',
  'main.seller.reviews.reviews-component' => 'App\\Http\\Livewire\\Main\\Seller\\Reviews\\ReviewsComponent',
  'main.seller.withdrawals.create-component' => 'App\\Http\\Livewire\\Main\\Seller\\Withdrawals\\CreateComponent',
  'main.seller.withdrawals.settings-component' => 'App\\Http\\Livewire\\Main\\Seller\\Withdrawals\\SettingsComponent',
  'main.seller.withdrawals.withdrawals-component' => 'App\\Http\\Livewire\\Main\\Seller\\Withdrawals\\WithdrawalsComponent',
  'main.sellers.sellers-component' => 'App\\Http\\Livewire\\Main\\Sellers\\SellersComponent',
  'main.service.service-component' => 'App\\Http\\Livewire\\Main\\Service\\ServiceComponent',
);