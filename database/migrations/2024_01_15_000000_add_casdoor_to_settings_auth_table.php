<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('settings_auth', function (Blueprint $table) {
            $table->boolean('is_casdoor_login')->default(false)->after('is_linkedin_login');
            $table->string('casdoor_server_url', 255)->nullable()->after('is_casdoor_login');
            $table->string('casdoor_application_name', 100)->nullable()->after('casdoor_server_url');
            $table->string('casdoor_client_id', 255)->nullable()->after('casdoor_application_name');
            $table->string('casdoor_client_secret', 255)->nullable()->after('casdoor_client_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('settings_auth', function (Blueprint $table) {
            $table->dropColumn([
                'is_casdoor_login',
                'casdoor_server_url',
                'casdoor_application_name',
                'casdoor_client_id',
                'casdoor_client_secret'
            ]);
        });
    }
};
